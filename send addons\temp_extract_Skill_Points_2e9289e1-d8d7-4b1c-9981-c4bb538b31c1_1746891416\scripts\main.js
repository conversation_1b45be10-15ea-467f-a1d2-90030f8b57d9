import { system, world, EffectTypes, MolangVariableMap } from "@minecraft/server";
import { ActionFormData, FormCancelationReason, UIManager } from "@minecraft/server-ui";
import { upgradeEveryXLevel } from "./config"

// WARNING: WRONG FILE, DO NOT TOUCH THIS FILE IF YOU DON'T KNOW WHAT YOU ARE DOING!!!

const prevEXPLevel = new Object();
const gameEffects = EffectTypes.getAll();
gameEffects.splice(3, 1);

function capitalize(text) {
    let array = text.toLowerCase().split('');
    array[0] = array[0].toUpperCase();

    for (let i = 1; i < array.length; i++) {
        if (!(array[i].toLowerCase() != array[i].toUpperCase())) {
            array[i] = ' ';
        } else if (array[i - 1] == ' ') {
            array[i] = array[i].toUpperCase();
        }
    }
    return array.join('');
}
function romanize(num) {
    if (isNaN(num))
        return NaN;
    var digits = String(+num).split(""),
        key = ["","C","CC","CCC","CD","D","DC","DCC","DCCC","CM",
               "","X","XX","XXX","XL","L","LX","LXX","LXXX","XC",
               "","I","II","III","IV","V","VI","VII","VIII","IX"],
        roman = "",
        i = 3;
    while (i--)
        roman = (key[+digits.pop() + (i * 10)] || "") + roman;
    return Array(+digits.join("") + 1).join("M") + roman;
}

function showSkillPointsGUI(player) {
    const dimension = player.dimension;
    const playerHeadLocation = {x: player.location.x, y: player.location.y +1.75, z: player.location.z};
    const actionForm = new ActionFormData().title({translate: "Choose a Skill"}); //Translate later
    gameEffects.forEach((effect) => {
        actionForm.button({translate: capitalize(effect.getName()) + (player.getEffect(effect) ? ` ${romanize(player.getEffect(effect)?.amplifier +2)}` : '')});
    });
    actionForm
      .show(player)
      .then((formResult) => {
        if (formResult.canceled) {
            showSkillPointsGUI(player);
            return -1;
        }
        const choosenEffect = gameEffects[formResult.selection];
        const newEffect = player.addEffect(choosenEffect, 20000000, {amplifier: (player.getEffect(choosenEffect)?.amplifier ?? -1) +1, showParticles: false}); // Make Effects Infinite
        world.sendMessage(`${player.name} has just reached lvl ${player.level} and obtained ${newEffect?.displayName}`); //DisplayName is Translated
        dimension.playSound('random.potion.brewed', playerHeadLocation, {pitch: 1.2, volume: 1});
        dimension.playSound('random.levelup', playerHeadLocation, {pitch: 1.2, volume: 1});
        dimension.spawnParticle('minecraft:totem_particle', playerHeadLocation);
    });
    dimension.playSound('beacon.power', playerHeadLocation, {pitch: 3, volume: 2});
}

system.runInterval(() => {
    world.getPlayers().forEach((player) => {
        const currentEXPLevel = player.level;
        if (currentEXPLevel === prevEXPLevel[player])
            return;
        const EXPLevelChange = currentEXPLevel - (prevEXPLevel[player] ?? (currentEXPLevel + 1));
        const nearestLevel = Math.round(currentEXPLevel / upgradeEveryXLevel) * upgradeEveryXLevel;
        if (currentEXPLevel === nearestLevel && EXPLevelChange === Math.abs(EXPLevelChange))
            showSkillPointsGUI(player);
        prevEXPLevel[player] = player.level;
    });
}, 80);

// SkillPoints Addon by JakeCCz
// =--=(https://mcpedl.com/user/JakeCCz/)=--= //