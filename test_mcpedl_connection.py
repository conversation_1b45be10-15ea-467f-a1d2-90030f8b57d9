#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاتصال مع موقع MCPEDL
"""

import sys
import os
import requests
import time

# إضافة مجلد send addons إلى المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
addons_dir = os.path.join(current_dir, 'send addons')
sys.path.insert(0, addons_dir)

def test_basic_connection():
    """اختبار الاتصال الأساسي"""
    print("🌐 اختبار الاتصال الأساسي مع MCPEDL")
    print("=" * 50)
    
    test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
    
    try:
        # اختبار ping بسيط
        print("📡 اختبار الاتصال...")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        # محاولة طلب HEAD أولاً
        print("🔍 محاولة طلب HEAD...")
        response = requests.head(test_url, headers=headers, timeout=10)
        print(f"✅ رمز الاستجابة: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ الموقع متاح")
            
            # محاولة طلب GET
            print("📄 محاولة جلب المحتوى...")
            response = requests.get(test_url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                content_length = len(response.text)
                print(f"✅ تم جلب المحتوى: {content_length} حرف")
                
                # فحص المحتوى
                if '<title>' in response.text:
                    print("✅ يحتوي على عنوان")
                else:
                    print("⚠️ لا يحتوي على عنوان")
                
                if 'dragon' in response.text.lower():
                    print("✅ يحتوي على محتوى المود")
                else:
                    print("⚠️ لا يحتوي على محتوى المود")
                
                return True
            else:
                print(f"❌ فشل جلب المحتوى: {response.status_code}")
                return False
        else:
            print(f"❌ الموقع غير متاح: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ انتهت مهلة الاتصال")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ خطأ في الاتصال")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_cloudscraper():
    """اختبار cloudscraper"""
    print("\n☁️ اختبار cloudscraper")
    print("=" * 50)
    
    try:
        import cloudscraper
        
        scraper = cloudscraper.create_scraper()
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        
        print("🔍 محاولة جلب الصفحة بـ cloudscraper...")
        response = scraper.get(test_url, timeout=30)
        
        if response.status_code == 200:
            content_length = len(response.text)
            print(f"✅ تم جلب المحتوى: {content_length} حرف")
            
            # فحص المحتوى
            if '<title>' in response.text:
                print("✅ يحتوي على عنوان")
            else:
                print("⚠️ لا يحتوي على عنوان")
            
            return True
        else:
            print(f"❌ فشل: {response.status_code}")
            return False
            
    except ImportError:
        print("❌ cloudscraper غير متوفر")
        return False
    except Exception as e:
        print(f"❌ خطأ في cloudscraper: {e}")
        return False

def test_selenium_basic():
    """اختبار Selenium الأساسي"""
    print("\n🤖 اختبار Selenium الأساسي")
    print("=" * 50)
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        print("🔧 إعداد Chrome...")
        
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--ignore-ssl-errors')
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-images')
        options.add_argument('--disable-javascript')
        
        driver = webdriver.Chrome(options=options)
        driver.set_page_load_timeout(20)
        
        try:
            test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
            print(f"🌐 جلب الصفحة: {test_url}")
            
            driver.get(test_url)
            
            # انتظار العنوان
            try:
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "title"))
                )
                print("✅ تم تحميل العنوان")
            except:
                print("⚠️ لم يتم تحميل العنوان")
            
            # فحص المحتوى
            page_source = driver.page_source
            content_length = len(page_source)
            print(f"📄 طول المحتوى: {content_length} حرف")
            
            if content_length > 1000:
                print("✅ تم جلب محتوى كافي")
                return True
            else:
                print("⚠️ محتوى قليل")
                return False
                
        finally:
            driver.quit()
            
    except ImportError:
        print("❌ Selenium غير متوفر")
        return False
    except Exception as e:
        print(f"❌ خطأ في Selenium: {e}")
        return False

def test_alternative_urls():
    """اختبار روابط بديلة"""
    print("\n🔗 اختبار روابط بديلة")
    print("=" * 50)
    
    alternative_urls = [
        "https://mcpedl.com/",
        "https://mcpedl.com/category/addons/",
        "https://mcpedl.com/simple-weapons/",
        "https://mcpedl.com/furniture-addon/"
    ]
    
    working_urls = []
    
    for url in alternative_urls:
        try:
            print(f"🔍 اختبار: {url}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                print(f"  ✅ يعمل ({len(response.text)} حرف)")
                working_urls.append(url)
            else:
                print(f"  ❌ لا يعمل ({response.status_code})")
                
        except Exception as e:
            print(f"  ❌ خطأ: {e}")
    
    print(f"\n📊 الروابط العاملة: {len(working_urls)}/{len(alternative_urls)}")
    return len(working_urls) > 0

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار الاتصال مع موقع MCPEDL")
    print("=" * 60)
    
    tests = [
        ("الاتصال الأساسي", test_basic_connection),
        ("cloudscraper", test_cloudscraper),
        ("Selenium الأساسي", test_selenium_basic),
        ("روابط بديلة", test_alternative_urls)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 اختبار: {test_name}")
        print("-" * 50)
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ نجح اختبار {test_name}")
            else:
                print(f"❌ فشل اختبار {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print(f"📊 النتائج النهائية: {passed_tests}/{total_tests} اختبارات نجحت")
    
    if passed_tests == 0:
        print("❌ جميع الاختبارات فشلت")
        print("\n💡 نصائح:")
        print("  1. تحقق من الاتصال بالإنترنت")
        print("  2. قد يكون الموقع يحجب الطلبات مؤقتاً")
        print("  3. جرب استخدام VPN")
        print("  4. جرب لاحقاً")
    elif passed_tests < total_tests:
        print("⚠️ بعض الاختبارات فشلت")
        print("💡 يمكن المحاولة مع الطرق التي نجحت")
    else:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ يمكن استخدام الأداة الرئيسية")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        input("\nاضغط Enter للخروج...")
