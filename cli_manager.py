"""
مدير واجهة سطر الأوامر المتقدم لأداة استخراج مودات mcpedl.com
"""
import argparse
import sys
import os
from typing import List, Optional
import json

from main import MCPEDLModExtractor
from data_analyzer import DataAnalyzer
from supabase_client import SupabaseClient
from utils import setup_logging, is_valid_mcpedl_url

class CLIManager:
    """مدير واجهة سطر الأوامر"""
    
    def __init__(self):
        self.extractor = None
        self.analyzer = None
        self.supabase_client = None
    
    def setup_parser(self) -> argparse.ArgumentParser:
        """إعداد محلل الأوامر"""
        parser = argparse.ArgumentParser(
            description='أداة شاملة لإدارة واستخراج بيانات مودات Minecraft من mcpedl.com',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
أمثلة الاستخدام:

استخراج البيانات:
  python cli_manager.py extract --url "https://mcpedl.com/mod-name/"
  python cli_manager.py extract --file urls.txt --no-skip-existing

تحليل البيانات:
  python cli_manager.py analyze --report
  python cli_manager.py analyze --export analysis.json

إدارة قاعدة البيانات:
  python cli_manager.py db --list --limit 10
  python cli_manager.py db --search "dragon"
  python cli_manager.py db --delete-id 123

أدوات مساعدة:
  python cli_manager.py utils --validate-urls urls.txt
  python cli_manager.py utils --generate-sample-urls 5
            """
        )
        
        subparsers = parser.add_subparsers(dest='command', help='الأوامر المتاحة')
        
        # أمر الاستخراج
        extract_parser = subparsers.add_parser('extract', help='استخراج بيانات المودات')
        extract_group = extract_parser.add_mutually_exclusive_group(required=True)
        extract_group.add_argument('--url', type=str, help='رابط مود واحد')
        extract_group.add_argument('--file', type=str, help='ملف يحتوي على روابط')
        extract_parser.add_argument('--no-skip-existing', action='store_true', 
                                  help='عدم تخطي المودات الموجودة')
        extract_parser.add_argument('--batch-size', type=int, default=10,
                                  help='عدد المودات في كل دفعة')
        
        # أمر التحليل
        analyze_parser = subparsers.add_parser('analyze', help='تحليل البيانات المخزنة')
        analyze_parser.add_argument('--report', action='store_true', help='عرض تقرير شامل')
        analyze_parser.add_argument('--export', type=str, help='تصدير التحليل إلى ملف JSON')
        analyze_parser.add_argument('--categories', action='store_true', help='تحليل الفئات فقط')
        analyze_parser.add_argument('--creators', action='store_true', help='تحليل المطورين فقط')
        
        # أمر إدارة قاعدة البيانات
        db_parser = subparsers.add_parser('db', help='إدارة قاعدة البيانات')
        db_parser.add_argument('--list', action='store_true', help='عرض قائمة المودات')
        db_parser.add_argument('--search', type=str, help='البحث في المودات')
        db_parser.add_argument('--delete-id', type=int, help='حذف مود بالمعرف')
        db_parser.add_argument('--delete-url', type=str, help='حذف مود بالرابط')
        db_parser.add_argument('--limit', type=int, default=20, help='عدد النتائج المعروضة')
        db_parser.add_argument('--export-all', type=str, help='تصدير جميع البيانات إلى ملف JSON')
        
        # أدوات مساعدة
        utils_parser = subparsers.add_parser('utils', help='أدوات مساعدة')
        utils_parser.add_argument('--validate-urls', type=str, help='التحقق من صحة الروابط في ملف')
        utils_parser.add_argument('--generate-sample-urls', type=int, help='توليد روابط عينة للاختبار')
        utils_parser.add_argument('--check-config', action='store_true', help='التحقق من الإعدادات')
        
        # خيارات عامة
        parser.add_argument('--verbose', action='store_true', help='عرض تفاصيل أكثر')
        parser.add_argument('--quiet', action='store_true', help='تقليل الرسائل')
        
        return parser
    
    def handle_extract_command(self, args):
        """معالجة أمر الاستخراج"""
        print("🚀 بدء عملية الاستخراج...")
        
        try:
            self.extractor = MCPEDLModExtractor()
            skip_existing = not args.no_skip_existing
            
            if args.url:
                success = self.extractor.process_single_mod(args.url, skip_existing)
                if success:
                    print("✅ تم استخراج المود بنجاح!")
                else:
                    print("❌ فشل في استخراج المود")
                    return False
            
            elif args.file:
                if not os.path.exists(args.file):
                    print(f"❌ الملف غير موجود: {args.file}")
                    return False
                
                results = self.extractor.process_from_file(args.file, skip_existing)
                print(f"\n📊 نتائج الاستخراج:")
                print(f"   إجمالي: {results['total']}")
                print(f"   نجح: {results['successful']}")
                print(f"   فشل: {results['failed']}")
                
                if results['failed'] > 0:
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في عملية الاستخراج: {e}")
            return False
        finally:
            if self.extractor:
                self.extractor.close()
    
    def handle_analyze_command(self, args):
        """معالجة أمر التحليل"""
        print("📊 بدء تحليل البيانات...")
        
        try:
            self.analyzer = DataAnalyzer()
            
            if args.report:
                report = self.analyzer.generate_report()
                print(report)
            
            if args.export:
                success = self.analyzer.export_analysis_to_file(args.export)
                if success:
                    print(f"✅ تم تصدير التحليل إلى {args.export}")
                else:
                    print("❌ فشل في تصدير التحليل")
                    return False
            
            if args.categories or args.creators:
                analysis = self.analyzer.analyze_mods_data()
                
                if args.categories and 'categories' in analysis:
                    print("\n📂 تحليل الفئات:")
                    for category, count in analysis['categories']['most_popular']:
                        print(f"   {category}: {count} مود")
                
                if args.creators and 'creators' in analysis:
                    print("\n👨‍💻 تحليل المطورين:")
                    for creator, count in analysis['creators']['most_active']:
                        print(f"   {creator}: {count} مود")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تحليل البيانات: {e}")
            return False
    
    def handle_db_command(self, args):
        """معالجة أمر إدارة قاعدة البيانات"""
        print("🗄️ إدارة قاعدة البيانات...")
        
        try:
            self.supabase_client = SupabaseClient()
            
            if args.list:
                mods = self.supabase_client.get_all_mods(args.limit)
                print(f"\n📋 قائمة المودات ({len(mods)} من أصل {args.limit}):")
                for mod in mods:
                    print(f"   ID: {mod['id']} | {mod['name']} | {mod.get('category', 'N/A')}")
            
            if args.search:
                # يمكن تحسين هذا بإضافة دالة بحث في SupabaseClient
                mods = self.supabase_client.get_all_mods(100)
                search_term = args.search.lower()
                filtered_mods = [
                    mod for mod in mods 
                    if search_term in mod.get('name', '').lower() or 
                       search_term in mod.get('description', '').lower()
                ]
                
                print(f"\n🔍 نتائج البحث عن '{args.search}' ({len(filtered_mods)} نتيجة):")
                for mod in filtered_mods[:args.limit]:
                    print(f"   ID: {mod['id']} | {mod['name']}")
            
            if args.delete_id:
                success = self.supabase_client.delete_mod(args.delete_id)
                if success:
                    print(f"✅ تم حذف المود ID: {args.delete_id}")
                else:
                    print(f"❌ فشل في حذف المود ID: {args.delete_id}")
                    return False
            
            if args.delete_url:
                mod = self.supabase_client.get_mod_by_url(args.delete_url)
                if mod:
                    success = self.supabase_client.delete_mod(mod['id'])
                    if success:
                        print(f"✅ تم حذف المود: {mod['name']}")
                    else:
                        print("❌ فشل في حذف المود")
                        return False
                else:
                    print("❌ لم يتم العثور على مود بهذا الرابط")
                    return False
            
            if args.export_all:
                mods = self.supabase_client.get_all_mods(10000)  # جلب جميع المودات
                with open(args.export_all, 'w', encoding='utf-8') as f:
                    json.dump(mods, f, ensure_ascii=False, indent=2)
                print(f"✅ تم تصدير {len(mods)} مود إلى {args.export_all}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إدارة قاعدة البيانات: {e}")
            return False
    
    def handle_utils_command(self, args):
        """معالجة أمر الأدوات المساعدة"""
        print("🔧 تشغيل الأدوات المساعدة...")
        
        try:
            if args.validate_urls:
                if not os.path.exists(args.validate_urls):
                    print(f"❌ الملف غير موجود: {args.validate_urls}")
                    return False
                
                with open(args.validate_urls, 'r', encoding='utf-8') as f:
                    urls = [line.strip() for line in f if line.strip()]
                
                valid_urls = []
                invalid_urls = []
                
                for url in urls:
                    if is_valid_mcpedl_url(url):
                        valid_urls.append(url)
                    else:
                        invalid_urls.append(url)
                
                print(f"\n✅ روابط صحيحة: {len(valid_urls)}")
                print(f"❌ روابط خاطئة: {len(invalid_urls)}")
                
                if invalid_urls:
                    print("\nالروابط الخاطئة:")
                    for url in invalid_urls:
                        print(f"   {url}")
            
            if args.generate_sample_urls:
                sample_urls = [
                    "https://mcpedl.com/dragon-mounts-v1-3-25/",
                    "https://mcpedl.com/pixelmon-mod/",
                    "https://mcpedl.com/furniture-mod/",
                    "https://mcpedl.com/cars-mod/",
                    "https://mcpedl.com/weapons-mod/"
                ]
                
                selected_urls = sample_urls[:args.generate_sample_urls]
                
                with open('sample_urls.txt', 'w', encoding='utf-8') as f:
                    for url in selected_urls:
                        f.write(url + '\n')
                
                print(f"✅ تم توليد {len(selected_urls)} رابط عينة في sample_urls.txt")
            
            if args.check_config:
                from config import Config
                try:
                    Config.validate_config()
                    print("✅ الإعدادات صحيحة")
                except Exception as e:
                    print(f"❌ خطأ في الإعدادات: {e}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الأدوات المساعدة: {e}")
            return False

def main():
    """الدالة الرئيسية"""
    cli_manager = CLIManager()
    parser = cli_manager.setup_parser()
    args = parser.parse_args()
    
    # إعداد نظام التسجيل
    if args.verbose:
        setup_logging(level='DEBUG')
    elif args.quiet:
        setup_logging(level='ERROR')
    else:
        setup_logging(level='INFO')
    
    # التحقق من وجود أمر
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # تنفيذ الأمر المطلوب
    success = False
    
    try:
        if args.command == 'extract':
            success = cli_manager.handle_extract_command(args)
        elif args.command == 'analyze':
            success = cli_manager.handle_analyze_command(args)
        elif args.command == 'db':
            success = cli_manager.handle_db_command(args)
        elif args.command == 'utils':
            success = cli_manager.handle_utils_command(args)
        else:
            print(f"❌ أمر غير معروف: {args.command}")
            parser.print_help()
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف العملية بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        sys.exit(1)
    
    # الخروج بحالة النجاح/الفشل
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
