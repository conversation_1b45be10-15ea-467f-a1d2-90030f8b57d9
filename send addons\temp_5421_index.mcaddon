<!DOCTYPE html>
<html lang="ru">
<head>
    
    <link rel="icon" href="https://mcpeland.io/favicon.ico" type="image/x-icon">
    <title>Скачать Мод Дроп с Воздуха для Выживания для Майнкрафт.</title>
<meta name="charset" content="utf-8">
<meta name="title" content="Скачать Мод Дроп с Воздуха для Выживания для Майнкрафт.">
<meta name="description" content="Это удивительное дополнение позволяет вам вызывать аирдропы прямо в ваш мир Майнкрафт, предоставление ценных предметов для повышения вашего выживания! Существует три типа аирдропов, каждый из которых предлагает определенные ресурсы для разных этапов прогресса.">
<meta name="keywords" content="дроп">
<meta name="generator" content="DataLife Engine (https://dle-news.ru)">
<link rel="canonical" href="https://mcpeland.io/mods/1516-mod-drop-s-vozduha-dlja-vyzhivanija.html">
<link rel="search" type="application/opensearchdescription+xml" title="mcpeland.io" href="https://mcpeland.io/index.php?do=opensearch">
<meta property="twitter:title" content="Скачать Мод Дроп с Воздуха для Выживания для Майнкрафт.">
<meta property="twitter:url" content="https://mcpeland.io/mods/1516-mod-drop-s-vozduha-dlja-vyzhivanija.html">
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:image" content="https://mcpeland.io/uploads/posts/2025-03/survivors-airdrop-addon_2.png">
<meta property="twitter:description" content="Это удивительное дополнение позволяет вам вызывать аирдропы прямо в ваш мир Майнкрафт, предоставление ценных предметов для повышения вашего выживания! Существует три типа аирдропов, каждый из которых предлагает определенные ресурсы для разных этапов прогресса. Как это работает? Крафт и">
<meta property="og:type" content="article">
<meta property="og:site_name" content="mcpeland.io">
<meta property="og:title" content="Скачать Мод Дроп с Воздуха для Выживания для Майнкрафт.">
<meta property="og:url" content="https://mcpeland.io/mods/1516-mod-drop-s-vozduha-dlja-vyzhivanija.html">
<meta property="og:image" content="https://mcpeland.io/uploads/posts/2025-03/survivors-airdrop-addon_2.png">
<meta property="og:description" content="Это удивительное дополнение позволяет вам вызывать аирдропы прямо в ваш мир Майнкрафт, предоставление ценных предметов для повышения вашего выживания! Существует три типа аирдропов, каждый из которых предлагает определенные ресурсы для разных этапов прогресса. Как это работает? Крафт и">
<link rel="alternate" hreflang="x-default" href="https://mcpeland.io/mods/1516-mod-drop-s-vozduha-dlja-vyzhivanija.html" />
<link rel="alternate" hreflang="ru" href="https://mcpeland.io/mods/1516-mod-drop-s-vozduha-dlja-vyzhivanija.html" />
<link rel="alternate" hreflang="en" href="https://mcpeland.io/en/mods/1516-addon-survivors-airdrop.html" />
    <meta name="HandheldFriendly" content="true">
    <meta name="format-detection" content="telephone=no">
    <meta name="viewport" content="user-scalable=no, initial-scale=1.0, maximum-scale=1.0, width=device-width">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    
    <link rel="preload" href="/templates/mcpeland/css/fonts/s/SF-UI-Text-Medium.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/templates/mcpeland/css/fonts/s/SF-UI-Text-Regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/templates/mcpeland/css/fonts/s/SF-UI-Text-Bold.woff2?1" as="font" type="font/woff2" crossorigin>

    <link href="/templates/mcpeland/css/reset.css?200" type="text/css" rel="stylesheet">
    <link href="/templates/mcpeland/css/popup.css?200" type="text/css" rel="stylesheet">
    <link href="/templates/mcpeland/css/style.css?v=1" type="text/css" rel="stylesheet">

    <!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-FYNTV53XC5"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-FYNTV53XC5');
</script>
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3593361370383111"
     crossorigin="anonymous"></script>
    </head>
<body>
<header>
    <div class="box flex middle between">
        <div>
            <div class="navToggle" role="button" aria-label="Открыть меню">
                <div class="icon"></div>
            </div>
        </div>
        <div>
            <div class="logo flex-md middle">
                <a href="/"><img src="/templates/mcpeland/img/logotip.png" alt="Minecraft pocket edition hub"></a>
                
            </div>
        </div>
        <div class="flex middle">
            <div class="header-social">
                
            </div>
            <div class="search">
                <span class="icon search-black-icon" ></span>
                <form class="search-form flex middle between" method="post" action="/index.php?do=search">
                    <div>
                        <span class="icon search-icon"></span>
                        <input class="regular black" name="story" type="search" placeholder="Поиск по сайту">
                        <input type="hidden" name="do" value="search">
                        <input type="hidden" name="subaction" value="search">
                    </div>
                    <span class="icon close-icon"></span>
                </form>
            </div>
        </div>
    </div>
</header>
<nav class="mainmenu">
    <div class="bg" ></div>
    <div class="flex middle">
        <div class="box">
            <div class="flex-md middle between">
                <a class="white-transparent regular " href="/">Главная</a>
                <a class="white-transparent regular " href="https://mcpeland.io/news/">Новости</a><a class="white-transparent regular " href="https://mcpeland.io/download/">Скачать Minecraft PE</a><a class="white-transparent regular active" href="https://mcpeland.io/mods/">Моды</a><a class="white-transparent regular " href="https://mcpeland.io/textures/">Текстуры</a><a class="white-transparent regular " href="https://mcpeland.io/shaders/">Шейдеры</a><a class="white-transparent regular " href="https://mcpeland.io/maps/">Карты</a><a class="white-transparent regular " href="https://mcpeland.io/skins/">Скины</a><a class="white-transparent regular " href="https://mcpeland.io/seeds/">Сиды</a><a class="white-transparent regular " href="https://mcpeland.io/programms/">Программы</a>

                <select onchange="window.location.href=this.value;">
					<option value="https://mcpeland.io/en/mods/1516-addon-survivors-airdrop.html"> English (en)</option>
<option value="https://mcpeland.io/mods/1516-mod-drop-s-vozduha-dlja-vyzhivanija.html" selected> Русский (ru)</option>
				</select> 
            </div>
        </div>
    </div>
</nav>
<style>
.news-item .news-image {
    position:relative;
    }
h2.category-name {
    position: absolute;
    display:block;
    bottom: 0px;
    left: 0px;
    letter-spacing: 1px;
    font-size: 12px;
    text-align: center;
    line-height: 1;    
    }
h2.category-name span {
    color: #fff;
    font-size: 12px;
    line-height: 55px;
    width:100%;
    /*background: rgba(0, 0, 0, 0.7);
    background: #000;*/
    padding: 10px;
    border-radius: 0px 0px 0 0px;
    text-transform: uppercase;
    }
    
    
    </style>    
<div class="layout news-list">
    <div>

                
        <div class="content">
            <div class="row">
                <div class="col-xs-12 col-md-8">
                    <div class="wrapper">
                        <nav id="dle-speedbar" data-nosnippet><div class="speedbar regular grey"><div class="over"><a href="https://mcpeland.io/">MCPELand.io</a> » <a href="https://mcpeland.io/mods/">Моды</a> » Мод: Дроп с Воздуха для Выживания</div></div></nav>
                        
                        <div id="dle-content"><style>
    .supported_version{display: flex;align-items: center;flex-wrap: wrap;}
    .supported_version a {font-size: 18px;font-weight: 600;color: var(--white);padding: 9px 8px;background: var(--green);transition: all .2s ease 0s;border-radius: 3px;}
</style>    
    
<article class="fullstory">
    <span class="date regular transparent-grey" data-nosnippet>17-03-2025, 01:49</span>
    <h1 class="bold black" data-nosnippet>Мод: Дроп с Воздуха для Выживания</h1>
    <div class="fullstory-info flex middle" data-nosnippet>
        <p class="regular transparent-grey">
        <div class="flex middle regular transparent-grey">
            <span class="icon views"></span>
            <span class="format-numb">5 571</span>
        </div>
        <div class="flex middle regular transparent-grey">
            <span class="icon comment-icon"></span>
            <span class="format-numb">0</span>
        </div>
        <div id='ratig-layer-1516'>
	<div class="rating">
		<ul class="unit-rating">
		<li class="current-rating" style="width:80%;">80</li>
		<li><a href="#" title="Плохо" class="r1-unit" onclick="doRate('1', '1516'); return false;">1</a></li>
		<li><a href="#" title="Приемлемо" class="r2-unit" onclick="doRate('2', '1516'); return false;">2</a></li>
		<li><a href="#" title="Средне" class="r3-unit" onclick="doRate('3', '1516'); return false;">3</a></li>
		<li><a href="#" title="Хорошо" class="r4-unit" onclick="doRate('4', '1516'); return false;">4</a></li>
		<li><a href="#" title="Отлично" class="r5-unit" onclick="doRate('5', '1516'); return false;">5</a></li>
		</ul>
	</div>
</div>
    </div>
    <div class="likely-wrap flex middle between" data-nosnippet>
        <div class="likely">
            <div class="vkontakte">1</div>
            <div class="twitter"></div>
            <div class="facebook"></div>
            <div class="telegram"></div>
        </div>
        <div class="grey regular">
        </div>
    </div>
    <div data-nosnippet class="fullstory-image">
        <img itemprop="image" srcset="/uploads/posts/2025-03/srsy_1-520x245.png 100w" sizes="100vw" src="/uploads/posts/2025-03/srsy_1-520x245.png" decoding="async" alt="Мод: Дроп с Воздуха для Выживания" width="640" height="313" style="object-fit:contain;height:auto;">
    </div>
    <div class="ad-contain">
    </div>
    <div class="fullstory-content regular black">
       <div>
<p><b>Это удивительное дополнение позволяет вам вызывать аирдропы прямо в ваш мир Майнкрафт, предоставление ценных предметов для повышения вашего выживания!</b> <br>Существует три типа аирдропов, каждый из которых предлагает определенные ресурсы для разных этапов прогресса. </p>
</div>
<div class="description-field">
<p style="text-align:center;"><mark class="marker-yellow"><strong><br><br></strong></mark><mark class="marker-yellow"><strong>Как это работает? </strong></mark><br><mark class="pen-red"><strong>Крафт и использование аирдропа: </strong></mark><br>Создавайте аирдропы, используя стандартные ресурсы Minecraft.<br><i><mark class="pen-green"><strong><img data-src="/uploads/posts/2025-03/survivors-airdrop-addon_2.png" alt="" style="display:block;margin-left:auto;margin-right:auto;"></strong><br></mark></i></p>
<p>После создания взаимодействуйте с предметом, чтобы запустить сигнал в небо. <br><img data-src="/uploads/posts/2025-03/medium/survivors-airdrop-addon_3.png" alt="" style="display:block;margin-left:auto;margin-right:auto;"> </p>
<p>Выбранный аирдроп опустится вниз, доставляя необходимые припасы! <br><img data-src="/uploads/posts/2025-03/survivors-airdrop-addon_4.png" alt="" style="display:block;margin-left:auto;margin-right:auto;"> </p>
<div class="nitro-content">
<div id="empty_to_skip_iteration"></div>
</div>
<div class="nitro-content">
<div id="mcpedl-nitro-video"></div>
</div>
<p style="text-align:center;"><mark class="marker-yellow"><strong>Виды аирдропов <br></strong></mark><br><mark class="pen-red"><strong>Базовый ( </strong></mark><mark class="marker-green"><strong>Зеленый </strong></mark><mark class="pen-red"><strong>): </strong></mark><br><b>Содержание:</b> Кольчужные доспехи, каменные орудия и морковь. <br><b>Лучше всего подходит для</b>: Ранние приключения или простые чрезвычайные ситуации <img data-src="/uploads/posts/2025-03/survivors-airdrop-addon_5.png" alt="" style="display:block;margin-left:auto;margin-right:auto;"> </p>
<p style="text-align:center;"><mark class="pen-red"><strong>Промежуточный ( </strong></mark><mark class="marker-blue"><strong>Синий </strong></mark><mark class="pen-red"><strong>): </strong></mark><br><b>Содержание:</b> Железные доспехи, железные инструменты, лук со стрелами, хлеб. <br><b>Лучше всего подходит для:</b> Игроки в середине прогресса, которым требуется улучшенное снаряжение. <img data-src="/uploads/posts/2025-03/survivors-airdrop-addon_6.png" alt="" style="display:block;margin-left:auto;margin-right:auto;"> </p>
<p style="text-align:center;"><mark class="pen-red"><strong>Продвинутый ( </strong></mark><mark class="marker-pink"><strong>Красный </strong></mark><mark class="pen-red"><strong>): </strong></mark><br><b>Содержание: </b> Алмазные доспехи и инструменты, щит, лук со стрелами, стейк и золотые яблоки. <br><b>Лучше всего подходит для: </b> Испытания высокой сложности или напряженные сражения. <img data-src="/uploads/posts/2025-03/survivors-airdrop-addon_7.png" alt="" style="display:block;margin-left:auto;margin-right:auto;"> </p>
<p style="text-align:left;"><mark class="marker-yellow"><strong>Преимущества  </strong></mark><br><b>Удобство: </b> Приобретайте предметы первой необходимости, где бы вы ни находились. <br><b>Многосторонность:</b> Выберите тип аирдропа в зависимости от ваших текущих потребностей. <br><b>Улучшенная выживаемость:</b> Получите доступ к предметам, которые повысят ваши шансы в бою и исследовании. <br><br><mark class="marker-yellow"><strong>Совместимость  </strong></mark><br>Чтобы избежать конфликтов с другими аддонами, разместите аддон в верхней части вашего списка действий или пакетов ресурсов. <br><b>Будьте готовы ко всему с этими аирдропами на выживание! <br><br></b></p>
<p style="text-align:left;"><div class="content-box">
    <div class="download-item border-radius" style="overflow: hidden;">
        <div class="item-content">
            <span class="black medium">Скачать Дроп с Воздуха для Выживания (.mcaddon)</span>
            <div class="flex-sm middle-sm">
                <a target="_blank" rel="nofollow" class="flex middle center green-bg medium white" href="https://mcpeland.io/index.php?do=download&id=2291" role="button">
                    <div class="flex middle center">
                        <span class="flex middle center"><span class="icon download-link-white"></span></span>
                        <div class="flex middle center">Скачать</div>
                    </div>
                </a>
                <p class="regular transparent-grey">[347.69 Kb] Скачиваний: 1684</p>
            </div>
        </div>
    </div>
</div> </p>
</div>
                                </div>
                                 <div class="fullstory-content regular black b-downloads-wrap">                                     
                                </div>
    
                                
                                <div class="ad-contain">
                                </div>            
                                <div>


                    <div class="fixed-download-btn-wrap">
                                    <div>
                                        <div class="row">
                                            <div class="col-xs-12 col-md-8">
                                                <div class="fixed-download-btn">
                                                    <button type="button" class="flex middle center green-bg medium white">
                                                        <div class="flex middle center">
                                                            <span class="flex middle center">
                                                                <span class="icon list-white"></span>
                                                                <span class="icon list-green"></span>
                                                            </span>
                                                            <div class="flex middle center">Скачать</div>
                                                        </div>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
<div class="fullstory-support-v border-radius">
<div class="support-text medium black">Поддерживаемые Версии</div>
<div class="block-list medium green">
 <p><a href="https://mcpeland.io/xfsearch/version/1.21.50/">1.21.50</a></p>, <p><a href="https://mcpeland.io/xfsearch/version/1.21.41/">1.21.41</a></p>, <p><a href="https://mcpeland.io/xfsearch/version/1.21.30/">1.21.30</a></p>, <p><a href="https://mcpeland.io/xfsearch/version/1.21.20/">1.21.20</a></p> 
</div>
</div>
                                    <div class="fullstory-author-info regular transparent-grey">
                                        <div class="flex middle">
                                            <p class="author">Опубликовал: <span><a href="https://mcpeland.io/user/Steve/">Steve</a></span></p>
                                            <p>Категория: <span>Моды</span></p>
                                            <p>ОS:  android, windows, iOS </p>
                                        </div>
                                        <div>
                                         <div class="raiting">   
                                         <div id='ratig-layer-1516'>
	<div class="rating">
		<ul class="unit-rating">
		<li class="current-rating" style="width:80%;">80</li>
		<li><a href="#" title="Плохо" class="r1-unit" onclick="doRate('1', '1516'); return false;">1</a></li>
		<li><a href="#" title="Приемлемо" class="r2-unit" onclick="doRate('2', '1516'); return false;">2</a></li>
		<li><a href="#" title="Средне" class="r3-unit" onclick="doRate('3', '1516'); return false;">3</a></li>
		<li><a href="#" title="Хорошо" class="r4-unit" onclick="doRate('4', '1516'); return false;">4</a></li>
		<li><a href="#" title="Отлично" class="r5-unit" onclick="doRate('5', '1516'); return false;">5</a></li>
		</ul>
	</div>
</div>
                                            </div>    
                                        </div>
                                    </div>
                                </div>
                                <div class="likely-wrap bottom">
                                    <div class="likely">
                                        <div class="vkontakte"></div>
                                        <div class="twitter"></div>
                                        <div class="facebook"></div>
                                        <div class="telegram"></div>
                                    </div>
                                </div>
                                <div class="related">
                                    <div class="flex middle between">
                                        <span class="bold black">Похожие статьи</span>
                                        <div class="flex middle">
                                            <div class="flex middle center prev" role="button">
                                                <span class="icon navigation-left"></span>
                                            </div>
                                            <div class="flex middle center next" role="button">
                                                <span class="icon navigation-right"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="related-layout">
                                        <div>
                                            <div class="row">
                                                <div>
<a class="related-news-item" href="https://mcpeland.io/mods/1545-mod-dorozhnye-dekoracii.html" title="Мод: Дорожные Декорации">
<div class="image">
<img decoding="async" srcset="/uploads/posts/2025-04/screenshot-41_1-520x245.png 100w" src="/uploads/posts/2025-04/screenshot-41_1-520x245.png" data-srcset="/uploads/posts/2025-04/screenshot-41_1-520x245.png 100w" class="lazyload" alt="Мод: Дорожные Декорации" sizes="100vw">
    
</div>
<p class="regular black">Мод: Дорожные Декорации</p>
</a>
</div><div>
<a class="related-news-item" href="https://mcpeland.io/mods/1544-mod-filorialy.html" title="Мод: Филориалы">
<div class="image">
<img decoding="async" srcset="/uploads/posts/2025-04/fis_1-520x245.png 100w" src="/uploads/posts/2025-04/fis_1-520x245.png" data-srcset="/uploads/posts/2025-04/fis_1-520x245.png 100w" class="lazyload" alt="Мод: Филориалы" sizes="100vw">
    
</div>
<p class="regular black">Мод: Филориалы</p>
</a>
</div><div>
<a class="related-news-item" href="https://mcpeland.io/mods/1543-mod-italjanskij-brejnrot.html" title="Мод: Итальянский Брейнрот">
<div class="image">
<img decoding="async" srcset="/uploads/posts/2025-04/img3767_1-520x245.png 100w" src="/uploads/posts/2025-04/img3767_1-520x245.png" data-srcset="/uploads/posts/2025-04/img3767_1-520x245.png 100w" class="lazyload" alt="Мод: Итальянский Брейнрот" sizes="100vw">
    
</div>
<p class="regular black">Мод: Итальянский Брейнрот</p>
</a>
</div><div>
<a class="related-news-item" href="https://mcpeland.io/mods/1542-mod-italjanskij-brejnrot.html" title="Мод: Проклятый Итальянский Брейнрот">
<div class="image">
<img decoding="async" srcset="/uploads/posts/2025-04/1745959948_screenshot_2025-04-26-16-01-13-617_com-mojang.jpg 100w" src="/uploads/posts/2025-04/1745959948_screenshot_2025-04-26-16-01-13-617_com-mojang.jpg" data-srcset="/uploads/posts/2025-04/1745959948_screenshot_2025-04-26-16-01-13-617_com-mojang.jpg 100w" class="lazyload" alt="Мод: Проклятый Итальянский Брейнрот" sizes="100vw">
    
</div>
<p class="regular black">Мод: Проклятый Итальянский Брейнрот</p>
</a>
</div><div>
<a class="related-news-item" href="https://mcpeland.io/mods/1541-mod-avto-iz-70-yh.html" title="Мод: Авто из 70-ых">
<div class="image">
<img decoding="async" srcset="/uploads/posts/2025-04/-20520250408230942_1-520x245.png 100w" src="/uploads/posts/2025-04/-20520250408230942_1-520x245.png" data-srcset="/uploads/posts/2025-04/-20520250408230942_1-520x245.png 100w" class="lazyload" alt="Мод: Авто из 70-ых" sizes="100vw">
    
</div>
<p class="regular black">Мод: Авто из 70-ых</p>
</a>
</div><div>
<a class="related-news-item" href="https://mcpeland.io/mods/1540-mod-zvezdnye-vojny-general-grivus.html" title="Мод: ЗВЁЗДНЫЕ ВОЙНЫ - ГЕНЕРАЛ ГРИВУС">
<div class="image">
<img decoding="async" srcset="/uploads/posts/2025-04/picsart_23-12-20_22-32-28-480-jpg.jpg 100w" src="/uploads/posts/2025-04/picsart_23-12-20_22-32-28-480-jpg.jpg" data-srcset="/uploads/posts/2025-04/picsart_23-12-20_22-32-28-480-jpg.jpg 100w" class="lazyload" alt="Мод: ЗВЁЗДНЫЕ ВОЙНЫ - ГЕНЕРАЛ ГРИВУС" sizes="100vw">
    
</div>
<p class="regular black">Мод: ЗВЁЗДНЫЕ ВОЙНЫ - ГЕНЕРАЛ ГРИВУС</p>
</a>
</div><div>
<a class="related-news-item" href="https://mcpeland.io/mods/1539-mod-ochki-x-ray.html" title="Мод: Очки X-Ray">
<div class="image">
<img decoding="async" srcset="/uploads/posts/2025-04/wans-xray-goggles_2.png 100w" src="/uploads/posts/2025-04/wans-xray-goggles_2.png" data-srcset="/uploads/posts/2025-04/wans-xray-goggles_2.png 100w" class="lazyload" alt="Мод: Очки X-Ray" sizes="100vw">
    
</div>
<p class="regular black">Мод: Очки X-Ray</p>
</a>
</div><div>
<a class="related-news-item" href="https://mcpeland.io/mods/1538-mod.html" title="Мод: Заполнитель Дыр">
<div class="image">
<img decoding="async" srcset="/uploads/posts/2025-04/holefilleraddon_1-520x245.png 100w" src="/uploads/posts/2025-04/holefilleraddon_1-520x245.png" data-srcset="/uploads/posts/2025-04/holefilleraddon_1-520x245.png 100w" class="lazyload" alt="Мод: Заполнитель Дыр" sizes="100vw">
    
</div>
<p class="regular black">Мод: Заполнитель Дыр</p>
</a>
</div><div>
<a class="related-news-item" href="https://mcpeland.io/mods/1537-mod-skazochnaja-mebel.html" title="Мод: Сказочная Мебель">
<div class="image">
<img decoding="async" srcset="/uploads/posts/2025-04/ff_img-png.png 100w" src="/uploads/posts/2025-04/ff_img-png.png" data-srcset="/uploads/posts/2025-04/ff_img-png.png 100w" class="lazyload" alt="Мод: Сказочная Мебель" sizes="100vw">
    
</div>
<p class="regular black">Мод: Сказочная Мебель</p>
</a>
</div><div>
<a class="related-news-item" href="https://mcpeland.io/mods/1536-mod-bezymjannoe-bozhestvo.html" title="Мод: Безымянное Божество">
<div class="image">
<img decoding="async" srcset="/uploads/posts/2025-04/packicon720x340_1-520x245.png 100w" src="/uploads/posts/2025-04/packicon720x340_1-520x245.png" data-srcset="/uploads/posts/2025-04/packicon720x340_1-520x245.png 100w" class="lazyload" alt="Мод: Безымянное Божество" sizes="100vw">
    
</div>
<p class="regular black">Мод: Безымянное Божество</p>
</a>
</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="comments-wrapper">
                                    <form  method="post" name="dle-comments-form" id="dle-comments-form" ><div class="addcomment">
    <div class="box_in">
        <span class="bold black">Оставить комментарий</span>
        <div class="comment-form-group">
            <div class="row middle">
                
                <div class="col-xs-12 col-sm-6">
                    <input class="black regular" placeholder="Имя" type="text" name="name" id="name" required>
                </div>
                <div class="col-xs-12 col-sm-6">
                    <input class="black regular" placeholder="E-mail (не обязательно)" type="email" name="mail" id="mail">
                </div>
                
                <div class="col-xs-12">
                    <div class="bb-editor">
                        <textarea name="comments" id="comments" cols="70" rows="10"></textarea>
                    </div>
                    
                </div>
                <div class="col-xs-12 col-sm-6 captcha end-sm">
                    
                    <div class="c-captcha flex middle end-sm">
                        <a onclick="reload(); return false;" title="Кликните на изображение чтобы обновить код, если он неразборчив" href="#"><span id="dle-captcha"><img src="/engine/modules/antibot/antibot.php" alt="Кликните на изображение чтобы обновить код, если он неразборчив" width="160" height="80"></span></a>
                        <input placeholder="Код с картинки" title="Введите код указанный на картинке" type="text" name="sec_code" id="sec_code" required>
                    </div>
                    
                </div>
                <div class="col-xs-12 col-sm-6 submit">
                    <button class="medium white green-bg" type="submit" name="submit" title="Отправить">Отправить</button>
                </div>
            </div>
        </div>
    </div>
</div>
		<input type="hidden" name="subaction" value="addcomment">
		<input type="hidden" name="post_id" id="post_id" value="1516"><input type="hidden" name="user_hash" value="9c3b944b54fcde5dca60fceadd21f12f299eef66"></form>
                                    <div id="dlemasscomments"><div id="dle-comments-list">
                                            <div id="dle-ajax-comments"></div>
                                            <div id="comment"></div><ol class="comments-tree-list">
                                        
<div id="dle-ajax-comments"></div>

                                        </ol></div></div>
                                </div>
                            </article></div><div id="dle-content"></div>
                    </div>                    
                </div>
                
                <div class="col-xs-12 col-md-4" data-nosnippet>
                                                   <div class="rightside row">
                        <div class="recomended-links col-xs-12 col-sm-6 col-md-12">
                            
                        </div>
                        <div class="usefull-links col-xs-12 col-sm-6 col-md-12">
                            
                        </div>
                     <div class="top-news col-xs-12 col-sm-6 col-md-12">
                            
                        </div>
                    </div>             





                </div>

                <script type="text/javascript" data-cfasync="false" src="/templates/mcpeland/js/lozad.js"></script>
                <script type="text/javascript" data-cfasync="false">
                    lozad('.lazyload').observe();
                </script>

            </div>
        </div>
    </div>
</div>

<div class="prefooter">
    
</div>
<footer>
    <div class="box">
        <div class="flex-sm middle between">
            <div class="flex-sm middle" style="display: block; margin-left: auto; margin-right: auto">
                <div>
                    <a class="logo flex center">
                        <img width="164" src="/templates/mcpeland/img/logo.png?v1">
                    </a>
                </div>
                <div class="footer-menu">
                    <ul class="flex center">
                        <li><a class="medium transparent-grey" href="/about.html">О проекте</a></li>                        
                        <li><a class="medium transparent-grey" href="/contacts.html">Контакты</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-social">
             
            </div>
        </div>
    </div>
</footer>
<link href="/templates/mcpeland/css/engine.css?205" type="text/css" rel="stylesheet">
<link href="/templates/mcpeland/css/fonts.css?v=2027" type="text/css" rel="stylesheet">

<script src="/engine/classes/js/jquery.js?v=98a2a"></script>
<script src="/engine/classes/js/jqueryui.js?v=98a2a" defer></script>
<script src="/engine/classes/js/dle_js.js?v=98a2a" defer></script>
<script src="/engine/classes/js/lazyload.js?v=98a2a" defer></script>
<script src="/engine/editor/jscripts/tiny_mce/tinymce.min.js?v=98a2a" defer></script>
<script type="application/ld+json">{"@context":"https://schema.org","@graph":[{"@type":"BreadcrumbList","@context":"https://schema.org/","itemListElement":[{"@type":"ListItem","position":1,"item":{"@id":"https://mcpeland.io/","name":"MCPELand.io"}},{"@type":"ListItem","position":2,"item":{"@id":"https://mcpeland.io/mods/","name":"Моды"}},{"@type":"ListItem","position":3,"item":{"@id":"https://mcpeland.io/mods/1516-mod-drop-s-vozduha-dlja-vyzhivanija.html","name":"Мод: Дроп с Воздуха для Выживания"}}]}]}</script>
<script>
<!--
var dle_root       = '/';
var dle_admin      = '';
var dle_login_hash = '9c3b944b54fcde5dca60fceadd21f12f299eef66';
var dle_group      = 5;
var dle_skin       = 'mcpeland';
var dle_wysiwyg    = '2';
var quick_wysiwyg  = '2';
var dle_min_search = '4';
var dle_act_lang   = ["Да", "Нет", "Ввод", "Отмена", "Сохранить", "Удалить", "Загрузка. Пожалуйста, подождите..."];
var menu_short     = 'Быстрое редактирование';
var menu_full      = 'Полное редактирование';
var menu_profile   = 'Просмотр профиля';
var menu_send      = 'Отправить сообщение';
var menu_uedit     = 'Админцентр';
var dle_info       = 'Информация';
var dle_confirm    = 'Подтверждение';
var dle_prompt     = 'Ввод информации';
var dle_req_field  = ["Заполните поле с именем", "Заполните поле с сообщением", "Заполните поле с темой сообщения"];
var dle_del_agree  = 'Вы действительно хотите удалить? Данное действие невозможно будет отменить';
var dle_spam_agree = 'Вы действительно хотите отметить пользователя как спамера? Это приведёт к удалению всех его комментариев';
var dle_c_title    = 'Отправка жалобы';
var dle_complaint  = 'Укажите текст Вашей жалобы для администрации:';
var dle_mail       = 'Ваш e-mail:';
var dle_big_text   = 'Выделен слишком большой участок текста.';
var dle_orfo_title = 'Укажите комментарий для администрации к найденной ошибке на странице:';
var dle_p_send     = 'Отправить';
var dle_p_send_ok  = 'Уведомление успешно отправлено';
var dle_save_ok    = 'Изменения успешно сохранены. Обновить страницу?';
var dle_reply_title= 'Ответ на комментарий';
var dle_tree_comm  = '0';
var dle_del_news   = 'Удалить статью';
var dle_sub_agree  = 'Вы действительно хотите подписаться на комментарии к данной публикации?';
var dle_captcha_type  = '0';
var dle_share_interesting  = ["Поделиться ссылкой на выделенный текст", "Twitter", "Facebook", "Вконтакте", "Прямая ссылка:", "Нажмите правой клавишей мыши и выберите «Копировать ссылку»"];
var DLEPlayerLang     = {prev: 'Предыдущий',next: 'Следующий',play: 'Воспроизвести',pause: 'Пауза',mute: 'Выключить звук', unmute: 'Включить звук', settings: 'Настройки', enterFullscreen: 'На полный экран', exitFullscreen: 'Выключить полноэкранный режим', speed: 'Скорость', normal: 'Обычная', quality: 'Качество', pip: 'Режим PiP'};
var allow_dle_delete_news   = false;
var dle_search_delay   = false;
var dle_search_value   = '';
jQuery(function($){

	
	
	tinyMCE.baseURL = dle_root + 'engine/editor/jscripts/tiny_mce';
	tinyMCE.suffix = '.min';

	tinymce.init({
		selector: 'textarea#comments',
		language : "ru",
		element_format : 'html',
		width : "100%",
		height : 260,

		deprecation_warnings: false,
		
		plugins: ["link image lists paste quickbars dlebutton noneditable"],
		
		draggable_modal: true,
		toolbar_mode: 'floating',
		contextmenu: false,
		relative_urls : false,
		convert_urls : false,
		remove_script_host : false,
		browser_spellcheck: true,
		extended_valid_elements : "div[align|style|class|contenteditable],b/strong,i/em,u,s,p[align|style|class|contenteditable]",
		quickbars_insert_toolbar: '',
		quickbars_selection_toolbar: 'bold italic underline | dlequote dlespoiler dlehide',
		
	    formats: {
	      bold: {inline: 'b'},
	      italic: {inline: 'i'},
	      underline: {inline: 'u', exact : true},
	      strikethrough: {inline: 's', exact : true}
	    },
		
		paste_as_text: true,
		elementpath: false,
		branding: false,
		
		dle_root : dle_root,
		dle_upload_area : "comments",
		dle_upload_user : "",
		dle_upload_news : "0",
		
		menubar: false,
		noneditable_editable_class: 'contenteditable',
		noneditable_noneditable_class: 'noncontenteditable',
		image_dimensions: false,
		
		
		toolbar: "bold italic underline | alignleft aligncenter alignright | bullist numlist | dleemo link dleleech  | dlequote dlespoiler dlehide",
		content_css : dle_root + "engine/editor/css/content.css"

	});
$('#dle-comments-form').submit(function() {
	doAddComments();
	return false;
});
FastSearch();
});
//-->
</script>
    
<link href="/templates/mcpeland/css/slick.css" type="text/css" rel="stylesheet">
<link href="/templates/mcpeland/css/slick-theme.css" type="text/css" rel="stylesheet">
<script type="0431389dd4372c71ec710669-text/javascript" src="/templates/mcpeland/js/slick.min.js?200"></script>

<link href="https://unpkg.com/ilyabirman-likely@2/release/likely.min.css" type="text/css" rel="stylesheet">
<script type="https://unpkg.com/ilyabirman-likely@2/release/likely.min.js"></script>    
    
<script type="2a70d1d371b918fb5f5e23f9-text/javascript" src="/templates/mcpeland/js/jquery.popup.min.js"></script>
    <script type="text/javascript" data-cfasync="false" src="/templates/mcpeland/js/lozad.js?2"></script>
    <script type="text/javascript" data-cfasync="false">
                	var mWidth = document.querySelector('.fullstory-content').offsetWidth;
                    var windowWidth = window.innerWidth;
                    var lElements = document.querySelectorAll(".lazyload");
                    lElements.forEach(function(element){
                        if(mWidth<element.getAttribute("width")){
                            element.style.height = element.getAttribute("height") * mWidth / element.getAttribute("width") + "px";
                        }
                        else{
                            element.style.height = element.getAttribute("height") + "px";
                        }

                    });
                    function onresizeListener(){
                        if(window.innerWidth != windowWidth){
                            var lElements = document.querySelectorAll(".lazyload");
                            lElements.forEach(function(element){
                                element.style.height = "auto";
                            });
                            window.removeEventListener("resize", onresizeListener, true);
                        }
                    }  
                    window.addEventListener("resize", onresizeListener, true);
                    lozad('.lazyload').observe();  
                	//setTimeout("lozad('.lazyload').observe();", 1800);
                	setInterval("lozad('.lazyload').observe();", 2000);
            </script>


        
<script>
    if (localStorage.likedNews !== undefined) {
    var GlobalAllUserLikes = JSON.parse(localStorage.likedNews);
} else {
    localStorage.likedNews = "[]";
    var GlobalAllUserLikes = JSON.parse(localStorage.likedNews);
}

function isLiked(newsId) {
    return GlobalAllUserLikes.includes(newsId);
}
(function() {
    var lastTime = 0;
    var vendors = ['ms', 'moz', 'webkit', 'o'];
    for (var x = 0; x < vendors.length && !window.requestAnimationFrame; ++x) {
        window.requestAnimationFrame = window[vendors[x] + 'RequestAnimationFrame'];
        window.cancelAnimationFrame = window[vendors[x] + 'CancelAnimationFrame'] || window[vendors[x] + 'CancelRequestAnimationFrame'];
    }
    if (!window.requestAnimationFrame)
        window.requestAnimationFrame = function(callback, element) {
            var currTime = new Date().getTime();
            var timeToCall = Math.max(0, 16 - (currTime - lastTime));
            var id = window.setTimeout(function() {
                callback(currTime + timeToCall);
            }, timeToCall);
            lastTime = currTime + timeToCall;
            return id;
        };
    if (!window.cancelAnimationFrame)
        window.cancelAnimationFrame = function(id) {
            clearTimeout(id);
        };
}());

function formatNumber(data) {
    var value = parseInt(data);
    return value >= 1000000 ? `${(value/1000000).toFixed(1)}M` : value >= 1000 ? `${(value/1000).toFixed(1)}K` : value;
}
$(document).ready(function() {
    $(window).scroll(function() {
        FixTopMenu();
    });
    if ($('.attachment') != null) {
        $('.attachment').each(function() {
            var dlVal = $(this).find('.attachment_size').html();
            var size = dlVal.substring(1, dlVal.length - 2);
            var i = 0,
                type = ['Р±', 'РљР±', 'РњР±', 'Р“Р±', 'РўР±', 'РџР±'];
            while ((size / 1000 | 0) && i < type.length - 1) {
                size /= 1024;
                i++;
            }
            $(this).find('.attachment_size').html('[' + size.toFixed(2) + ' ' + type[i] + '] ');
        });
    };
    $('.title_spoiler').click(function() {
        $(this).toggleClass('opened').next('.text_spoiler').slideToggle(200);
        return false;
    });
    $('.format-numb').toArray().forEach(function(item, i) {
        item.innerHTML = formatNumber(item.innerHTML.replace(/\s+/g, ''));
    });
    if (document.getElementById('fullstoryImage') != undefined) {
        var evItem = document.getElementById('fullstoryImage');
        var evItemImg = evItem.querySelector('img');
        var evItemImgSrc = evItemImg.getAttribute('src');
        var ua = window.navigator.userAgent;
        var msie = ua.indexOf('MSIE ');
        var trident = ua.indexOf('Trident/');
        var edge = ua.indexOf('Edge/');
        if (msie > 0) {
            evItem.style.backgroundImage = 'url(' + evItemImgSrc + ')';
            evItemImg.style.opacity = '0';
        } else if (trident > 0) {
            evItem.style.backgroundImage = 'url(' + evItemImgSrc + ')';
            evItemImg.style.opacity = '0';
        } else if (edge > 0) {
            evItem.style.backgroundImage = 'url(' + evItemImgSrc + ')';
            evItemImg.style.opacity = '0';
        }
    }
    $(".navToggle").on("click", function() {
        if (!($('nav').hasClass("open"))) {
            $('html,body').addClass("open");
            $('nav').addClass("open");
        } else {
            $('html,body').removeClass("open");
            $('nav').removeClass("open");
        }
        $(this).toggleClass("open");
    });
    $("nav > .bg").on("click", function() {
        $('html,body').removeClass("open");
        $('nav').removeClass("open");
        $('.navToggle').removeClass("open");
    })
    $('header .search > span.search-black-icon').click(function() {
        $('header .search-form').addClass('open');
    });
    $('header .search-form > span.close-icon').click(function() {
        $('header .search-form').removeClass('open');
    });

    function initSlider(sliderLayout) {
        var sliderEl = $(sliderLayout.querySelector('.pics-slider > ul')),
            withCounter = $(sliderLayout).hasClass('w-counter'),
            withSlidesDesc = $(sliderLayout).hasClass('w-slides-desc'),
            withNavigation = $(sliderLayout).hasClass('w-navigation');
        var slideInfoEl = null,
            navItems = null,
            imagesLength = 0,
            slidesDescs = [];

        function onChange(index, el) {
            if (withNavigation && navItems && el) {
                navItems.removeClass('active');
                el.classList.add('active');
                var navEl = sliderLayout.querySelector('.pics-nav > div'),
                    scrollLeft = navEl.scrollLeft,
                    windowWidth = navEl.offsetWidth,
                    elLeft = el.offsetLeft;
                if (elLeft < scrollLeft) navEl.scrollTo({
                    top: 0,
                    left: (scrollLeft - windowWidth / 2),
                    behavior: 'smooth'
                });
                else if ((elLeft + 100) > (scrollLeft + windowWidth)) navEl.scrollTo({
                    top: 0,
                    left: elLeft,
                    behavior: 'smooth'
                });
            }
            if (withCounter && slideInfoEl) {
                slideInfoEl.innerHTML = `<span class="regular white">${index+1} РёР· ${imagesLength}</span>`;
            } else if (withSlidesDesc && slideInfoEl) {
                slideInfoEl.innerHTML = `<span class="regular white">${slidesDescs[index]}</span>`;
            }
        }
        if (withCounter) {
            var div = document.createElement('div');
            div.className = "flex center";
            sliderLayout.querySelector('.pics-slider').appendChild(div);
            slideInfoEl = div;
            imagesLength = sliderLayout.querySelectorAll(`.pics-slider ul > li`).length;
        } else if (withSlidesDesc) {
            slidesDescs = [...sliderLayout.querySelectorAll(`.pics-slider ul > li`)].map(it => it.querySelector('img').getAttribute('alt'));
            var div = document.createElement('div');
            div.className = "flex center";
            sliderLayout.querySelector('.pics-slider').appendChild(div);
            slideInfoEl = div;
        };
        if (withNavigation) {
            var sliderParentEl = sliderLayout.querySelector('.pics-slider'),
                slideItems = sliderParentEl.querySelectorAll('li');
            var rowWrapper = document.createElement('div');
            rowWrapper.className = "row"
            slideItems.forEach((item, i) => {
                var _div_ = document.createElement('div');
                _div_.innerHTML = `<div class="slider-nav-item">${item.innerHTML}</div>`;
                rowWrapper.appendChild(_div_);
            });
            var div = document.createElement('div');
            div.className = "pics-nav";
            div.innerHTML = `<div>${rowWrapper.outerHTML}</div>`;
            sliderParentEl.parentNode.insertBefore(div, sliderParentEl.nextSibling);
            navItems = $(div.querySelectorAll('.slider-nav-item'));
            navItems.click(function() {
                sliderEl.slick('slickGoTo', navItems.index(this));
            });
        };
        sliderEl.slick({
            infinite: true,
            slidesToShow: 1,
            slidesToScroll: 1,
            adaptiveHeight: true,
            arrows: true,
            dots: false,
            prevArrow: '<button type="button" class="arrow flex middle center prev"><span class="icon navigation-left"></span></button>',
            nextArrow: '<button type="button" class="arrow flex middle center next"><span class="icon navigation-right"></span></button>'
        });
        sliderEl.on('beforeChange', function(event, slick, currentSlide, nextSlide) {
            onChange(nextSlide, withNavigation && navItems ? navItems[nextSlide] : null);
        });
        onChange(0, withNavigation && navItems ? navItems[0] : null);
    }
    if (document.querySelector('.pics-gallery')) {
        $('.pics-gallery').toArray().forEach(function(item, i) {
            initSlider(item);
        });
    }
    $('.fullstory .build-pics > .header').click(function() {
        $('.fullstory .build-pics').toggleClass('open');
    });
    if (document.querySelector('.rating .unit-rating')) {
        var ratingBody = $('.rating .unit-rating'),
            ratingValue = ratingBody.attr('data-value'),
            ratingLink = $(".rating .unit-rating > li > a");

        function setActiveRating(value) {
            document.querySelectorAll('.rating .unit-rating li').forEach(function(item, i) {
                var itemValue = item.getAttribute('data-value');
                if (itemValue <= value) item.classList.add('active');
                else item.classList.remove('active');
            });
        }

        function updRating() {
            ratingBody = $('.rating .unit-rating');
            ratingValue = ratingBody.attr('data-value');
            ratingLink = $(".rating .unit-rating > li > a");
            setActiveRating(ratingValue);
            ratingLink.click(function() {
                setTimeout(function() {
                    updRating();
                }, 1000);
            });
            ratingBody.on({
                mouseenter: function() {
                    var activeValue = $(this).attr('data-value');
                    setActiveRating(activeValue);
                },
                mouseleave: function() {
                    setActiveRating(ratingValue);
                }
            }, 'li');
        }
        updRating(ratingValue);
    }
    if (document.querySelector('.related-layout')) {
        var relatedSliderLayout = $('.related > .related-layout'),
            relatedSlider = document.querySelector('.related > .related-layout > div'),
            prevBtn = $('.related > .flex > div > .flex.prev'),
            nextBtn = $('.related > .flex > div > .flex.next');
        var windowWidth = relatedSlider.offsetWidth;
        prevBtn.click(function() {
            var scrollLeft = relatedSlider.scrollLeft;
            windowWidth = relatedSlider.offsetWidth;
            relatedSlider.scrollTo({
                top: 0,
                left: (scrollLeft - windowWidth),
                behavior: 'smooth'
            });
        });
        nextBtn.click(function() {
            var scrollLeft = relatedSlider.scrollLeft;
            windowWidth = relatedSlider.offsetWidth;
            relatedSlider.scrollTo({
                top: 0,
                left: (scrollLeft + windowWidth),
                behavior: 'smooth'
            });
        });

        function onRelatedSliderScroll() {
            var scrollLeft = relatedSlider.scrollLeft,
                scrollWidth = relatedSlider.scrollWidth;
            if (scrollLeft < 48) {
                prevBtn.addClass('disabled');
                nextBtn.removeClass('disabled');
                relatedSliderLayout.removeClass('right-border');
            } else if ((scrollLeft + windowWidth) >= (scrollWidth - 48)) {
                prevBtn.removeClass('disabled');
                nextBtn.addClass('disabled');
                relatedSliderLayout.addClass('right-border');
            } else {
                prevBtn.removeClass('disabled');
                nextBtn.removeClass('disabled');
                relatedSliderLayout.removeClass('right-border');
            }
        }
        onRelatedSliderScroll();
        relatedSlider.onscroll = onRelatedSliderScroll;
    }
    if (document.querySelector('.downloads-nav')) {
        var downloadSliderLayout = $('.downloads-nav > .downloads-slider'),
            downloadSlider = document.querySelector('.downloads-nav > .downloads-slider > div'),
            prevBtn = $('.downloads-nav > .flex > div > .flex.prev'),
            nextBtn = $('.downloads-nav > .flex > div > .flex.next');
        var windowWidth = downloadSlider.offsetWidth;
        prevBtn.click(function() {
            var scrollLeft = downloadSlider.scrollLeft;
            windowWidth = downloadSlider.offsetWidth;
            downloadSlider.scrollTo({
                top: 0,
                left: (scrollLeft - (windowWidth * 0.7)),
                behavior: 'smooth'
            });
        });
        nextBtn.click(function() {
            var scrollLeft = downloadSlider.scrollLeft;
            windowWidth = downloadSlider.offsetWidth;
            downloadSlider.scrollTo({
                top: 0,
                left: (scrollLeft + (windowWidth * 0.7)),
                behavior: 'smooth'
            });
        });

        function onDownloadSliderScroll() {
            var scrollLeft = downloadSlider.scrollLeft,
                scrollWidth = downloadSlider.scrollWidth;
            if (scrollLeft < 48) {
                prevBtn.addClass('disabled');
                nextBtn.removeClass('disabled');
                downloadSliderLayout.removeClass('right-border');
            } else if ((scrollLeft + windowWidth) >= (scrollWidth - 48)) {
                prevBtn.removeClass('disabled');
                nextBtn.addClass('disabled');
                downloadSliderLayout.addClass('right-border');
            } else {
                prevBtn.removeClass('disabled');
                nextBtn.removeClass('disabled');
                downloadSliderLayout.removeClass('right-border');
            }
        }
        onDownloadSliderScroll();
        downloadSlider.onscroll = onDownloadSliderScroll;
        let drag = false,
            startx = downloadSlider.scrollLeft,
            starty = downloadSlider.scrollTop,
            diffx = 0,
            diffy = 0;

        function onMouseDown(e) {
            if (!e) {
                e = window.event;
            }
            if (e.target && e.target.nodeName === 'IMG') {
                e.preventDefault();
            } else if (e.srcElement && e.srcElement.nodeName === 'IMG') {
                e.returnValue = false;
            }
            startx = e.clientX + downloadSlider.scrollLeft;
            starty = e.clientY + downloadSlider.scrollTop;
            diffx = 0;
            diffy = 0;
            drag = true;
        }

        function onMouseMove(e) {
            if (drag === true) {
                if (!e) {
                    e = window.event;
                }
                diffx = (startx - (e.clientX + downloadSlider.scrollLeft));
                diffy = (starty - (e.clientY + downloadSlider.scrollTop));
                downloadSlider.scrollLeft += diffx;
                downloadSlider.scrollTop += diffy;
                downloadSliderLayout.addClass('onmove')
            }
        }

        function onMouseUp(e) {
            if (!e) {
                e = window.event;
            }
            drag = false;
            var start = 1,
                animate = function() {
                    var step = Math.sin(start);
                    if (step <= 0) {
                        window.cancelAnimationFrame(animate);
                    } else {
                        downloadSlider.scrollLeft += diffx * step;
                        downloadSlider.scrollTop += diffy * step;
                        start -= 0.02;
                        window.requestAnimationFrame(animate);
                    }
                };
            animate();
            setTimeout(() => {
                downloadSliderLayout.removeClass('onmove')
            }, 50);
        }
        downloadSlider.addEventListener('mousedown', onMouseDown, false);
        window.addEventListener('mousemove', onMouseMove, false);
        window.addEventListener('mouseup', onMouseUp, false);
        downloadSlider.addEventListener('click', function(e) {}, false);
    }
    if (document.querySelector('.fullstory .fixed-download-btn-wrap') && document.querySelector('.b-downloads-wrap')) {
        var canShow = false,
            downloadsWrapper = document.querySelector('.b-downloads-wrap'),
            windowHeight = window.innerHeight,
            downloadsTop = downloadsWrapper.offsetTop,
            downloadsBottom = downloadsTop + downloadsWrapper.offsetHeight;

        function toggleDownloadsWrapper() {
            if (!canShow) return;
            var scrollTop = $(document).scrollTop();
            if ((scrollTop + windowHeight) > (downloadsTop + 200) && scrollTop < (downloadsBottom - 100)) {
                $('.fullstory .fixed-download-btn-wrap').removeClass('shown');
            } else {
                $('.fullstory .fixed-download-btn-wrap').addClass('shown');
            }
        }
        setTimeout(function() {
            canShow = true;
            toggleDownloadsWrapper();
        }, 750)
        toggleDownloadsWrapper();
        $(window).scroll(function() {
            toggleDownloadsWrapper();
        });
        $(window).resize(function() {
            windowHeight = window.innerHeight;
            downloadsTop = downloadsWrapper.offsetTop;
            downloadsBottom = downloadsTop + downloadsWrapper.offsetHeight;
        });
        $('.fullstory .fixed-download-btn-wrap .fixed-download-btn button').click(function() {
            $('html, body').animate({
                scrollTop: $(downloadsWrapper).offset().top,
                scrollLeft: 0
            }, 1000);
        })
    }
    if (document.querySelector('.fullstory .youtube-video')) {
        $('.fullstory .youtube-video > div').click(function() {
            var videoUrl = $(this).find('img').attr('data-mce-src');
            var ifrm = document.createElement("iframe");
            ifrm.setAttribute('src', videoUrl);
            ifrm.setAttribute('frameborder', "0");
            ifrm.setAttribute('allowfullscreen', true);
            $(this).parent().append(ifrm);
            $(this).remove();
        })
    }
    $('#skinVisual .tabs li').click(function() {
        var skinType = $(this).attr('data-item');
        $('#skinVisual .skinType').not('#' + skinType).fadeOut(0);
        $('#skinVisual .' + skinType).fadeIn(0);
        $('#skinVisual .tabs li').not(this).removeClass('active');
        $(this).addClass('active');
    });
    checkElem('.news-item', crossBrows('.news-item', '.news-image'));
    checkElem('.top-news-item', crossBrows('.top-news-item', '.image'));
    checkElem('.related-news-item', crossBrows('.related-news-item', '.image'));
    checkElem('.comment', crossBrows('.comment', '.left-ava'));
    checkElem('#preFootImgCont', crossBrows('#preFootImgCont', '#preFootImg'));
    $("#navigator").on("click", "a", function(event) {
        event.preventDefault();
        var elemId = $(this).attr('href');
        if (document.querySelector(elemId)) window.scrollTo({
            top: $(elemId).offset().top,
            left: 0,
            behavior: 'smooth'
        });
    });
});

function FixTopMenu() {
    if ($(document).scrollTop() >= 76) $('nav.mainmenu').addClass('fixed');
    else $('nav.mainmenu').removeClass('fixed');
}

function crossBrows(elem, imgCont) {
    $(elem).each(function() {
        if (!this.querySelector(imgCont)) return false;
        var evItem = this.querySelector(imgCont);
        var evItemImg = evItem.querySelector('img');
        var evItemImgSrc = evItemImg.getAttribute('src');
        var ua = window.navigator.userAgent;
        var msie = ua.indexOf('MSIE ');
        var trident = ua.indexOf('Trident/');
        var edge = ua.indexOf('Edge/');
        if (msie > 0) {
            evItem.style.backgroundImage = 'url(' + evItemImgSrc + ')';
            evItemImg.style.opacity = '0';
        } else if (trident > 0) {
            evItem.style.backgroundImage = 'url(' + evItemImgSrc + ')';
            evItemImg.style.opacity = '0';
        } else if (edge > 0) {
            evItem.style.backgroundImage = 'url(' + evItemImgSrc + ')';
            evItemImg.style.opacity = '0';
        }
    });
}

function checkElem(elem, func) {
    if (elem != undefined) func;
}

function openItem(el) {
    closeAllMore();
    if (el.parentElement.style.overflow != 'hidden') el.parentElement.style.overflow = 'hidden';
    el.parentElement.classList.toggle('closed')
}

function openMoreInfo(el) {
    closeAllMore();
    if (el.parentElement.parentElement.parentElement.parentElement.style.overflow === 'hidden') {
        el.parentElement.parentElement.parentElement.parentElement.style.overflow = 'visible';
    }
    el.parentElement.classList.add('showed')
}

function closeAllMore() {
    document.querySelectorAll('.content-box > .download-item > .item-content > ul > li').forEach(function(el) {
        el.classList.remove('showed');
    })
}
$(document).on('click touchstart', '.easylike_count', function(event) {
    event.preventDefault();
    var $this = $(this),
        news_id = $this.data('news-id'),
        btn_text = $this.data('btn-text')
    count = $this.data('count');
    if ($this.hasClass('liked')) {
        return;
    }
    $(`.easylike_count[data-news-id="${news_id}"]`).toArray().forEach(function(item, i) {
        item.classList.add('liked');
    });
    if (!btn_text) $this.html('<span class="easylike_load"><i class="easylike_circles ec1"></i><i class="easylike_circles ec2"></i><i class="easylike_circles ec3"></i></span>');
    $.post(dle_root + "engine/ajax/superlike_onlike.php", {
        news_id: news_id,
        count: count
    }, function(data) {
        GlobalAllUserLikes.push(news_id);
        localStorage.setItem('likedNews', JSON.stringify(GlobalAllUserLikes));
        $(`.easylike_count[data-news-id="${news_id}"]`).toArray().forEach(function(item, i) {
            if (!item.hasAttribute('data-btn-text')) item.innerHTML = formatNumber(data);
        });
    });
});
$(document).ready(function() {
    var likeElement = document.querySelector('.fullstory > .fullstory-info > div > .easylike_count');
    if (likeElement != null && likeElement != undefined) {
        var data = {
            news_id: [likeElement.getAttribute("data-news-id")]
        };
        $.post(dle_root + "engine/ajax/superlike.php", data, function(data) {
            if (data.length == 0) {
                likeElement.innerHTML = formatNumber(0);
                likeElement.setAttribute("data-count", 0);
                return;
            }
            likeElement.innerHTML = formatNumber(data[0].likes);
            likeElement.setAttribute("data-count", data[0].likes);
            if (isLiked(parseInt(likeElement.getAttribute("data-news-id")))) {
                likeElement.classList.add("liked");
                document.querySelector('.fullstory .likely-wrap > div.grey.regular > span.easylike_count').classList.add("liked");
            }
        }, "json");
        document.querySelector('.fullstory > .fullstory-info > div > .easylike_count').classList.add("loaded");
    }
});
</script>    

    <!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-FYNTV53XC5"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-FYNTV53XC5');
</script>
    </body>
</html>

<!-- DataLife Engine Copyright SoftNews Media Group (http://dle-news.ru) -->
