#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحسينات النهائية لاستخراج MCPEDL
- اختصار اسم المود
- فصل الوصف العربي عن الإنجليزي
- تحسين فلترة الصور والروابط
"""

import sys
import os

# إضافة مجلد send addons إلى المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
addons_dir = os.path.join(current_dir, 'send addons')
sys.path.insert(0, addons_dir)

def test_mod_name_shortening():
    """اختبار اختصار اسم المود"""
    print("📝 اختبار اختصار اسم المود")
    print("=" * 40)
    
    try:
        from mcpedl_selenium_scraper import EnhancedMCPEDLExtractor
        
        extractor = EnhancedMCPEDLExtractor()
        
        # أسماء للاختبار
        test_names = [
            "Dragon Mounts: Community Edition",
            "Furniture Mod: Ultimate",
            "Cars Addon: Enhanced",
            "Simple Weapons: Plus",
            "Magic Spells: Remastered",
            "Normal Mod Name"  # بدون إضافات
        ]
        
        expected_results = [
            "Dragon Mounts",
            "Furniture Mod",
            "Cars Addon",
            "Simple Weapons",
            "Magic Spells",
            "Normal Mod Name"
        ]
        
        all_passed = True
        
        for i, test_name in enumerate(test_names):
            shortened = extractor.shorten_mod_name(test_name)
            expected = expected_results[i]
            
            if shortened == expected:
                print(f"✅ '{test_name}' → '{shortened}'")
            else:
                print(f"❌ '{test_name}' → '{shortened}' (متوقع: '{expected}')")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ خطأ في اختبار اختصار الأسماء: {e}")
        return False

def test_ai_description_parsing():
    """اختبار تحليل وصف AI المنفصل"""
    print("\n🤖 اختبار تحليل وصف AI")
    print("=" * 40)
    
    try:
        from mcpedl_selenium_scraper import EnhancedMCPEDLExtractor
        
        extractor = EnhancedMCPEDLExtractor()
        
        # استجابة AI تجريبية
        test_ai_response = """
[English]
Dragon Mounts brings the legendary power of dragons to your Minecraft world. This comprehensive addon features multiple rideable dragon types, each with unique abilities and characteristics. Players can tame, breed, and ride these magnificent creatures while exploring dragon lairs filled with rare treasures.

[Arabic]
Dragon Mounts يجلب قوة التنانين الأسطورية إلى عالم ماين كرافت الخاص بك. تتضمن هذه الإضافة الشاملة أنواع متعددة من التنانين القابلة للركوب، كل منها بقدرات وخصائص فريدة. يمكن للاعبين ترويض وتربية وركوب هذه المخلوقات الرائعة.
"""
        
        parsed = extractor.parse_ai_response(test_ai_response)
        
        if parsed and isinstance(parsed, dict):
            english_desc = parsed.get('english', '')
            arabic_desc = parsed.get('arabic', '')
            
            print(f"الوصف الإنجليزي ({len(english_desc)} حرف):")
            print(f"  {english_desc[:100]}...")
            
            print(f"الوصف العربي ({len(arabic_desc)} حرف):")
            print(f"  {arabic_desc[:100]}...")
            
            # التحقق من وجود النص العربي والإنجليزي
            has_arabic = any(ord(char) > 1536 and ord(char) < 1791 for char in arabic_desc)
            has_english = any(char.isalpha() and ord(char) < 128 for char in english_desc)
            
            if has_arabic and has_english and len(english_desc) > 50 and len(arabic_desc) > 50:
                print("✅ تم تحليل الوصف بنجاح")
                return True
            else:
                print("❌ فشل في تحليل الوصف بشكل صحيح")
                return False
        else:
            print("❌ لم يتم إرجاع بيانات صحيحة")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحليل AI: {e}")
        return False

def test_complete_extraction():
    """اختبار الاستخراج الكامل مع رابط حقيقي"""
    print("\n🌐 اختبار الاستخراج الكامل")
    print("=" * 40)
    
    try:
        from mcpedl_selenium_scraper import scrape_mcpedl_with_selenium
        
        # رابط تجريبي (يمكن تغييره)
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        
        print(f"اختبار الاستخراج من: {test_url}")
        print("⚠️ هذا قد يستغرق وقتاً...")
        
        # استخراج البيانات
        mod_data = scrape_mcpedl_with_selenium(test_url)
        
        if mod_data:
            print("✅ تم الاستخراج بنجاح!")
            
            # فحص البيانات المستخرجة
            name = mod_data.get('name', '')
            description = mod_data.get('description', '')
            description_arabic = mod_data.get('description_arabic', '')
            images = mod_data.get('image_urls', [])
            creator = mod_data.get('creator_name', '')
            social_channels = mod_data.get('creator_social_channels', [])
            
            print(f"📝 الاسم: {name}")
            print(f"📄 الوصف الإنجليزي: {len(description)} حرف")
            print(f"📄 الوصف العربي: {len(description_arabic)} حرف")
            print(f"🖼️ الصور: {len(images)} صورة")
            print(f"👤 المطور: {creator}")
            print(f"🔗 قنوات التواصل: {len(social_channels)} قناة")
            
            # فحص جودة البيانات
            quality_score = 0
            total_checks = 6
            
            if name and len(name) > 3:
                quality_score += 1
                print("  ✅ الاسم صالح")
            
            if description and len(description) > 100:
                quality_score += 1
                print("  ✅ الوصف الإنجليزي مفصل")
            
            if description_arabic and len(description_arabic) > 100:
                quality_score += 1
                print("  ✅ الوصف العربي مفصل")
            
            if images and len(images) >= 3:
                quality_score += 1
                print("  ✅ صور كافية")
            
            if creator and len(creator) > 2:
                quality_score += 1
                print("  ✅ اسم المطور صالح")
            
            if social_channels and len(social_channels) >= 1:
                quality_score += 1
                print("  ✅ قنوات تواصل متوفرة")
            
            print(f"\n📊 نقاط الجودة: {quality_score}/{total_checks}")
            
            if quality_score >= 5:
                print("🎉 جودة ممتازة!")
                return True
            elif quality_score >= 3:
                print("👍 جودة جيدة")
                return True
            else:
                print("⚠️ جودة منخفضة")
                return False
        else:
            print("❌ فشل الاستخراج")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاستخراج الكامل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار التحسينات النهائية لاستخراج MCPEDL")
    print("=" * 60)
    
    tests = [
        ("اختصار اسم المود", test_mod_name_shortening),
        ("تحليل وصف AI", test_ai_description_parsing),
        ("الاستخراج الكامل", test_complete_extraction)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 اختبار: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ نجح اختبار {test_name}")
            else:
                print(f"❌ فشل اختبار {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print(f"📊 النتائج النهائية: {passed_tests}/{total_tests} اختبارات نجحت")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ التحسينات تعمل بشكل مثالي")
    elif passed_tests >= total_tests * 0.75:
        print("👍 معظم الاختبارات نجحت")
        print("⚠️ قد تحتاج بعض التحسينات الطفيفة")
    else:
        print("⚠️ بعض الاختبارات فشلت")
        print("🔧 قد تحتاج مراجعة الكود")
    
    print("\n💡 الآن يمكنك تجربة الأداة الرئيسية:")
    print('python "send addons/mod_processor.py"')
    
    print("\n🎯 التحسينات المطبقة:")
    print("- ✅ اختصار اسم المود (Dragon Mounts: Community Edition → Dragon Mounts)")
    print("- ✅ فصل الوصف العربي عن الإنجليزي في حقول منفصلة")
    print("- ✅ تحسين فلترة الصور (إزالة الصور الثابتة)")
    print("- ✅ تحسين فلترة روابط التواصل (إزالة الروابط الثابتة)")
    print("- ✅ تنظيف اسم المطور من التواريخ والنصوص الإضافية")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        input("\nاضغط Enter للخروج...")
