# -*- coding: utf-8 -*-
"""
اختبار الإصلاحات الجديدة: فلترة الصور وتحسين الأوصاف
"""

import sys
import os

# إضافة المجلد الحالي إلى المسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_image_filtering():
    """اختبار فلترة الصور المحسنة"""
    print("🖼️ اختبار فلترة الصور المحسنة...")
    
    try:
        from mcpedl_scraper_module import scrape_mcpedl_mod
        
        # اختبار مع ezRTX (المود الذي كان يستخرج صور المستخدمين)
        test_url = "https://mcpedl.com/ezrtx/"
        print(f"اختبار الرابط: {test_url}")
        
        result = scrape_mcpedl_mod(test_url)
        
        if result:
            images = result.get('image_urls', [])
            print(f"✅ تم استخراج {len(images)} صورة")
            
            # فحص جودة الصور
            mod_images = 0
            user_images = 0
            
            for i, img_url in enumerate(images, 1):
                print(f"   {i}. {img_url}")
                
                # فحص نوع الصورة
                if any(pattern in img_url.lower() for pattern in [
                    'media.forgecdn.net/attachments',
                    'mcpedl.com/wp-content'
                ]):
                    mod_images += 1
                elif any(pattern in img_url.lower() for pattern in [
                    '/users/', 'gravatar.com', 'r2.mcpedl.com/users'
                ]):
                    user_images += 1
            
            print(f"📊 تحليل الصور:")
            print(f"   - صور المود: {mod_images}")
            print(f"   - صور المستخدمين: {user_images}")
            
            if user_images == 0 and mod_images > 0:
                print("✅ تم تصفية صور المستخدمين بنجاح!")
                return True
            elif user_images > 0:
                print("❌ ما زالت هناك صور مستخدمين")
                return False
            else:
                print("⚠️ لم يتم العثور على صور مود")
                return False
        else:
            print("❌ فشل في استخراج البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الصور: {e}")
        return False

def test_gemini_descriptions():
    """اختبار الأوصاف المحسنة مع Gemini"""
    print("\n🤖 اختبار الأوصاف المحسنة مع Gemini...")
    
    try:
        from mcpedl_scraper_module import scrape_mcpedl_mod
        
        test_url = "https://mcpedl.com/ezrtx/"
        print(f"اختبار الرابط: {test_url}")
        
        result = scrape_mcpedl_mod(test_url)
        
        if result:
            english_desc = result.get('description', '')
            arabic_desc = result.get('description_arabic', '')
            
            print(f"📝 الوصف الإنجليزي ({len(english_desc)} حرف):")
            print(f"   {english_desc[:300]}...")
            
            print(f"📝 الوصف العربي ({len(arabic_desc)} حرف):")
            print(f"   {arabic_desc[:300]}...")
            
            # فحص جودة الأوصاف
            english_quality = check_advanced_description_quality(english_desc, 'english')
            arabic_quality = check_advanced_description_quality(arabic_desc, 'arabic')
            
            if english_quality and arabic_quality:
                print("✅ أوصاف عالية الجودة مع Gemini")
                return True
            elif english_quality or arabic_quality:
                print("⚠️ وصف واحد عالي الجودة")
                return True
            else:
                print("❌ الأوصاف تحتاج تحسين")
                return False
        else:
            print("❌ فشل في استخراج البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الأوصاف: {e}")
        return False

def check_advanced_description_quality(description: str, language: str) -> bool:
    """فحص متقدم لجودة الوصف"""
    if not description or len(description) < 200:
        print(f"   ❌ الوصف {language} قصير جداً ({len(description)} حرف)")
        return False
    
    # فحص الكلمات المهمة
    if language == 'english':
        required_elements = [
            'minecraft',
            'features',
            'key features',
            'download'
        ]
        
        quality_indicators = [
            'enhance', 'transform', 'incredible', 'amazing',
            'experience', 'gameplay', 'compatible'
        ]
        
    else:  # arabic
        required_elements = [
            'ماين كرافت',
            'مميزات',
            'المميزات الرئيسية'
        ]
        
        quality_indicators = [
            'عزز', 'حوّل', 'رائع', 'مذهل',
            'تجربة', 'اللعب', 'متوافق'
        ]
    
    # فحص العناصر المطلوبة
    desc_lower = description.lower()
    required_found = sum(1 for element in required_elements if element in desc_lower)
    quality_found = sum(1 for indicator in quality_indicators if indicator in desc_lower)
    
    # فحص وجود نقاط المميزات
    has_bullet_points = '•' in description or '*' in description or '-' in description
    
    # فحص الطول المناسب
    good_length = 300 <= len(description) <= 800
    
    print(f"   📊 تحليل الوصف {language}:")
    print(f"      - العناصر المطلوبة: {required_found}/{len(required_elements)}")
    print(f"      - مؤشرات الجودة: {quality_found}/{len(quality_indicators)}")
    print(f"      - نقاط المميزات: {'✅' if has_bullet_points else '❌'}")
    print(f"      - الطول المناسب: {'✅' if good_length else '❌'}")
    
    # تقييم الجودة
    score = 0
    if required_found >= len(required_elements) * 0.7:  # 70% من العناصر المطلوبة
        score += 1
    if quality_found >= 2:  # على الأقل مؤشرين للجودة
        score += 1
    if has_bullet_points:
        score += 1
    if good_length:
        score += 1
    
    if score >= 3:
        print(f"   ✅ الوصف {language} عالي الجودة ({score}/4)")
        return True
    else:
        print(f"   ⚠️ الوصف {language} يحتاج تحسين ({score}/4)")
        return False

def test_specific_mod_images():
    """اختبار استخراج صور مود محدد"""
    print("\n🎯 اختبار استخراج صور مود محدد...")
    
    try:
        from mcpedl_scraper_module import scrape_mcpedl_mod
        
        # اختبار مع مود يحتوي على صور متعددة
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        print(f"اختبار الرابط: {test_url}")
        
        result = scrape_mcpedl_mod(test_url)
        
        if result:
            images = result.get('image_urls', [])
            print(f"✅ تم استخراج {len(images)} صورة")
            
            # البحث عن صور forgecdn المتوقعة
            forgecdn_images = [img for img in images if 'media.forgecdn.net' in img]
            
            print(f"📊 صور forgecdn: {len(forgecdn_images)}")
            for img in forgecdn_images:
                print(f"   ✅ {img}")
            
            if len(forgecdn_images) >= 1:
                print("✅ تم العثور على صور المود الصحيحة")
                return True
            else:
                print("❌ لم يتم العثور على صور المود المتوقعة")
                return False
        else:
            print("❌ فشل في استخراج البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الصور المحددة: {e}")
        return False

def test_api_key_detection():
    """اختبار اكتشاف مفتاح API"""
    print("\n🔑 اختبار اكتشاف مفتاح Gemini API...")
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        
        extractor = MCPEDLExtractorFixed()
        api_key = extractor.get_gemini_api_key()
        
        if api_key:
            print(f"✅ تم العثور على مفتاح API: {api_key[:10]}...{api_key[-5:]}")
            
            # اختبار المفتاح
            try:
                import google.generativeai as genai
                genai.configure(api_key=api_key)
                model = genai.GenerativeModel('gemini-pro')
                
                response = model.generate_content("Say hello in one word")
                if response and response.text:
                    print(f"✅ المفتاح يعمل: {response.text.strip()}")
                    return True
                else:
                    print("❌ المفتاح لا يعمل")
                    return False
                    
            except Exception as e:
                print(f"❌ خطأ في اختبار المفتاح: {e}")
                return False
        else:
            print("⚠️ لم يتم العثور على مفتاح API")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار اكتشاف المفتاح: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🔧 اختبار الإصلاحات الجديدة")
    print("=" * 50)
    
    results = {}
    
    # اختبار اكتشاف مفتاح API
    results['api_key_detection'] = test_api_key_detection()
    
    # اختبار فلترة الصور
    results['image_filtering'] = test_image_filtering()
    
    # اختبار الأوصاف المحسنة
    results['gemini_descriptions'] = test_gemini_descriptions()
    
    # اختبار صور مود محدد
    results['specific_mod_images'] = test_specific_mod_images()
    
    # تلخيص النتائج
    print("\n📊 ملخص النتائج:")
    print("=" * 50)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name}: {status}")
    
    # تقييم عام
    success_rate = sum(results.values()) / len(results)
    print(f"\nمعدل النجاح: {success_rate*100:.1f}%")
    
    if success_rate >= 0.8:
        print("\n🎉 الإصلاحات نجحت!")
        print("✅ فلترة صور محسنة")
        print("✅ أوصاف Gemini عالية الجودة")
        print("✅ استخراج صور المود الصحيحة")
    elif success_rate >= 0.5:
        print("\n👍 الإصلاحات نجحت جزئياً")
        print("⚠️ قد تحتاج بعض التحسينات")
    else:
        print("\n⚠️ الإصلاحات تحتاج مراجعة")

if __name__ == "__main__":
    main()
