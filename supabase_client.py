"""
عميل قاعدة بيانات Supabase لتخزين بيانات المودات
"""
import json
import logging
from typing import Dict, List, Optional, Any
from supabase import create_client, Client

from config import Config

class SupabaseClient:
    """عميل قاعدة بيانات Supabase"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.client: Optional[Client] = None
        self._initialize_client()
    
    def _initialize_client(self):
        """تهيئة عميل Supabase"""
        try:
            if not Config.SUPABASE_URL or not Config.SUPABASE_SERVICE_KEY:
                raise ValueError("متغيرات بيئة Supabase مفقودة")
            
            self.client = create_client(
                Config.SUPABASE_URL,
                Config.SUPABASE_SERVICE_KEY
            )
            self.logger.info("تم تهيئة عميل Supabase بنجاح")
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة عميل Supabase: {e}")
            raise
    
    def _prepare_mod_data(self, mod_data: Dict[str, Any]) -> Dict[str, Any]:
        """تجهيز بيانات المود للإدراج في قاعدة البيانات"""
        prepared_data = {}
        
        # نسخ البيانات الأساسية
        basic_fields = [
            'name', 'description', 'description_ar', 'category', 
            'version', 'size', 'download_url', 'source_url',
            'creator_name', 'creator_contact_info', 'downloads', 'likes'
        ]
        
        for field in basic_fields:
            if field in mod_data:
                prepared_data[field] = mod_data[field]
        
        # تحويل قوائم الصور إلى JSON
        if 'image_urls' in mod_data and isinstance(mod_data['image_urls'], list):
            prepared_data['image_urls'] = json.dumps(mod_data['image_urls'])
        
        # تحويل قنوات التواصل الاجتماعي إلى JSON
        if 'creator_social_channels' in mod_data and isinstance(mod_data['creator_social_channels'], list):
            prepared_data['creator_social_channels'] = json.dumps(mod_data['creator_social_channels'])
        
        # تنظيف البيانات الفارغة
        cleaned_data = {}
        for key, value in prepared_data.items():
            if value is not None and value != '':
                cleaned_data[key] = value
        
        return cleaned_data
    
    def insert_mod(self, mod_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """إدراج مود جديد في قاعدة البيانات"""
        if not self.client:
            self.logger.error("عميل Supabase غير مهيأ")
            return None
        
        try:
            # تجهيز البيانات
            prepared_data = self._prepare_mod_data(mod_data)
            
            if not prepared_data.get('name'):
                self.logger.error("اسم المود مطلوب للإدراج")
                return None
            
            self.logger.info(f"إدراج مود جديد: {prepared_data['name']}")
            
            # إدراج البيانات
            response = self.client.table('mods').insert(prepared_data).execute()
            
            if response.data:
                self.logger.info(f"تم إدراج المود بنجاح: ID {response.data[0].get('id')}")
                return response.data[0]
            else:
                self.logger.error("فشل في إدراج المود - لا توجد بيانات في الاستجابة")
                return None
                
        except Exception as e:
            self.logger.error(f"خطأ في إدراج المود: {e}")
            return None
    
    def update_mod(self, mod_id: int, mod_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحديث مود موجود في قاعدة البيانات"""
        if not self.client:
            self.logger.error("عميل Supabase غير مهيأ")
            return None
        
        try:
            # تجهيز البيانات
            prepared_data = self._prepare_mod_data(mod_data)
            
            self.logger.info(f"تحديث المود: ID {mod_id}")
            
            # تحديث البيانات
            response = self.client.table('mods').update(prepared_data).eq('id', mod_id).execute()
            
            if response.data:
                self.logger.info(f"تم تحديث المود بنجاح: ID {mod_id}")
                return response.data[0]
            else:
                self.logger.error(f"فشل في تحديث المود: ID {mod_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"خطأ في تحديث المود: {e}")
            return None
    
    def get_mod_by_url(self, source_url: str) -> Optional[Dict[str, Any]]:
        """البحث عن مود بواسطة رابط المصدر"""
        if not self.client:
            self.logger.error("عميل Supabase غير مهيأ")
            return None
        
        try:
            response = self.client.table('mods').select('*').eq('source_url', source_url).execute()
            
            if response.data:
                return response.data[0]
            return None
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن المود: {e}")
            return None
    
    def get_mod_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """البحث عن مود بواسطة الاسم"""
        if not self.client:
            self.logger.error("عميل Supabase غير مهيأ")
            return None
        
        try:
            response = self.client.table('mods').select('*').eq('name', name).execute()
            
            if response.data:
                return response.data[0]
            return None
            
        except Exception as e:
            self.logger.error(f"خطأ في البحث عن المود: {e}")
            return None
    
    def mod_exists(self, source_url: str) -> bool:
        """التحقق من وجود مود بنفس رابط المصدر"""
        return self.get_mod_by_url(source_url) is not None
    
    def get_all_mods(self, limit: int = 100) -> List[Dict[str, Any]]:
        """جلب جميع المودات من قاعدة البيانات"""
        if not self.client:
            self.logger.error("عميل Supabase غير مهيأ")
            return []
        
        try:
            response = self.client.table('mods').select('*').limit(limit).execute()
            return response.data or []
            
        except Exception as e:
            self.logger.error(f"خطأ في جلب المودات: {e}")
            return []
    
    def delete_mod(self, mod_id: int) -> bool:
        """حذف مود من قاعدة البيانات"""
        if not self.client:
            self.logger.error("عميل Supabase غير مهيأ")
            return False
        
        try:
            response = self.client.table('mods').delete().eq('id', mod_id).execute()
            
            if response.data:
                self.logger.info(f"تم حذف المود بنجاح: ID {mod_id}")
                return True
            else:
                self.logger.error(f"فشل في حذف المود: ID {mod_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في حذف المود: {e}")
            return False
    
    def upsert_mod(self, mod_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """إدراج أو تحديث مود (upsert)"""
        source_url = mod_data.get('source_url')
        if not source_url:
            self.logger.error("رابط المصدر مطلوب للعملية")
            return None
        
        # التحقق من وجود المود
        existing_mod = self.get_mod_by_url(source_url)
        
        if existing_mod:
            # تحديث المود الموجود
            return self.update_mod(existing_mod['id'], mod_data)
        else:
            # إدراج مود جديد
            return self.insert_mod(mod_data)
