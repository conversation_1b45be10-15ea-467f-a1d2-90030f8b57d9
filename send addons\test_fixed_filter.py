# -*- coding: utf-8 -*-
"""
اختبار الفلتر المصحح لرفض الصور الفارغة
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_empty_image_filter():
    """اختبار فلترة الصور الفارغة"""
    print("🧪 اختبار فلترة الصور الفارغة")
    print("=" * 50)
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        
        extractor = MCPEDLExtractorFixed()
        
        # صور للاختبار
        test_images = [
            # صور يجب رفضها
            "https://mcpedl.com/img/empty.png",                                    # صورة فارغة
            "https://mcpedl.com/_nuxt/img/logo.e39b598.png",                      # شعار الموقع
            "https://r2.mcpedl.com/users/386393/avatar.png",                      # صورة مستخدم
            "https://secure.gravatar.com/avatar/eb191aa7c0c32503faafeed581afb077", # gravatar
            
            # صور يجب قبولها
            "https://media.forgecdn.net/attachments/1154/487/mcpedl-png.PNG",      # صورة مود حقيقية
            "https://media.forgecdn.net/attachments/1158/457/slide1-png.PNG",     # صورة مود حقيقية
            "https://api.mcpedl.com/storage/submissions/75334/103/image.png",     # صورة محتوى
            "https://mcpedl.com/wp-content/uploads/2024/mod-screenshot.jpg",      # صورة محتوى
        ]
        
        print("🔍 اختبار فلترة كل صورة:")
        
        accepted_count = 0
        rejected_count = 0
        
        for i, img_url in enumerate(test_images, 1):
            is_valid = extractor.is_definitely_mod_image(img_url)
            
            # تحديد ما إذا كانت النتيجة صحيحة
            should_accept = i > 4  # أول 4 صور يجب رفضها، الباقي يجب قبولها
            is_correct = (is_valid and should_accept) or (not is_valid and not should_accept)
            
            status = "✅" if is_correct else "❌"
            action = "قُبلت" if is_valid else "رُفضت"
            expected = "يجب قبولها" if should_accept else "يجب رفضها"
            
            print(f"   {status} [{i}] {action} ({expected}): {img_url[:60]}...")
            
            if is_correct:
                if should_accept:
                    accepted_count += 1
                else:
                    rejected_count += 1
        
        print(f"\n📊 نتائج الاختبار:")
        print(f"   ✅ قبول صحيح: {accepted_count}/4")
        print(f"   ❌ رفض صحيح: {rejected_count}/4")
        print(f"   🎯 دقة إجمالية: {(accepted_count + rejected_count)}/8")
        
        success = (accepted_count + rejected_count) >= 7
        
        if success:
            print("🎉 الفلتر يعمل بشكل ممتاز!")
        else:
            print("⚠️ الفلتر يحتاج تحسين")
        
        return success
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_real_extraction():
    """اختبار استخراج حقيقي مع HTML محاكي"""
    print("\n" + "=" * 50)
    print("🧪 اختبار استخراج حقيقي")
    print("=" * 50)
    
    # HTML محاكي يحتوي على صور مختلطة
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta property="og:image" content="https://media.forgecdn.net/attachments/1154/487/main-image.PNG">
        <title>Test Mod | MCPEDL</title>
    </head>
    <body>
        <div class="post-page__content">
            <h1>Test Mod</h1>
            
            <!-- صور يجب قبولها -->
            <img src="https://media.forgecdn.net/attachments/1158/457/screenshot1.PNG" alt="Screenshot 1">
            <img src="https://media.forgecdn.net/attachments/1180/337/screenshot2.PNG" alt="Screenshot 2">
            <img src="https://api.mcpedl.com/storage/submissions/75334/103/feature.png" alt="Feature">
            
            <!-- صور يجب رفضها -->
            <img src="https://mcpedl.com/img/empty.png" alt="Empty">
            <img src="https://mcpedl.com/_nuxt/img/logo.png" alt="Logo">
            
        </div>
        
        <!-- قسم "You may also like" -->
        <div class="related-posts">
            <h3>You may also like</h3>
            <img src="https://mcpedl.com/img/empty.png" alt="Related">
        </div>
        
        <!-- التعليقات -->
        <div class="comments">
            <img src="https://r2.mcpedl.com/users/123/avatar.png" alt="User">
        </div>
    </body>
    </html>
    """
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        
        extractor = MCPEDLExtractorFixed()
        
        print("🔄 اختبار استخراج كامل...")
        mod_data = extractor.extract_mod_data(test_html, "https://mcpedl.com/test-mod/", generate_ai_descriptions=False)
        
        if mod_data:
            images = mod_data.get('image_urls', [])
            print(f"✅ تم استخراج {len(images)} صورة")
            
            if images:
                print("   📋 الصور المستخرجة:")
                
                forgecdn_count = 0
                api_count = 0
                empty_count = 0
                
                for i, img in enumerate(images, 1):
                    print(f"      [{i}] {img}")
                    
                    if 'media.forgecdn.net' in img:
                        print(f"           ✅ صورة مود حقيقية (forgecdn)")
                        forgecdn_count += 1
                    elif 'api.mcpedl.com' in img:
                        print(f"           ✅ صورة محتوى (api)")
                        api_count += 1
                    elif '/img/empty.png' in img:
                        print(f"           ❌ صورة فارغة (يجب رفضها!)")
                        empty_count += 1
                    else:
                        print(f"           ⚠️ صورة أخرى")
                
                print(f"\n📊 تحليل النتائج:")
                print(f"   🔥 صور forgecdn: {forgecdn_count}")
                print(f"   📄 صور api: {api_count}")
                print(f"   ❌ صور فارغة: {empty_count}")
                print(f"   📊 إجمالي: {len(images)}")
                
                # تقييم الجودة
                if empty_count == 0 and forgecdn_count >= 2:
                    print("🎉 ممتاز! تم تجنب الصور الفارغة واستخراج صور حقيقية")
                    return True
                elif empty_count == 0:
                    print("👍 جيد! تم تجنب الصور الفارغة")
                    return True
                else:
                    print("❌ مشكلة! تم استخراج صور فارغة")
                    return False
            else:
                print("   ❌ لم يتم استخراج أي صور")
                return False
        else:
            print("❌ فشل الاستخراج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار الفلتر المصحح")
    print("=" * 60)
    
    # اختبار فلترة الصور
    success1 = test_empty_image_filter()
    
    # اختبار استخراج حقيقي
    success2 = test_real_extraction()
    
    print("\n" + "=" * 60)
    print("📊 النتائج النهائية:")
    print(f"   🎯 فلترة الصور: {'✅ نجح' if success1 else '❌ فشل'}")
    print(f"   🎯 استخراج حقيقي: {'✅ نجح' if success2 else '❌ فشل'}")
    
    if success1 and success2:
        print("\n🎉 الإصلاحات تعمل بشكل مثالي!")
        print("💡 الآن يجب أن تحصل على صور مود حقيقية فقط")
    else:
        print("\n⚠️ هناك مشكلة تحتاج مراجعة")

if __name__ == "__main__":
    main()
