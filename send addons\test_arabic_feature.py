#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json

# Change to the correct directory
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# Test Gemini import
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
    print("✅ Gemini library imported successfully")
except ImportError as e:
    GEMINI_AVAILABLE = False
    print(f"❌ Gemini import failed: {e}")

# Test config loading
CONFIG_FILE = "config.json"

def load_config():
    default_config = {"gemini_api_keys": []}
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                if "gemini_api_keys" not in config or not isinstance(config["gemini_api_keys"], list):
                    config["gemini_api_keys"] = []
                return config
        else:
            print(f"❌ Config file {CONFIG_FILE} not found")
            return default_config
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return default_config

# Test configuration
print(f"📁 Current directory: {os.getcwd()}")
print(f"📄 Config file exists: {os.path.exists(CONFIG_FILE)}")

config = load_config()
api_keys = config.get("gemini_api_keys", [])
print(f"🔑 API keys found: {len(api_keys)}")

if api_keys and GEMINI_AVAILABLE:
    print(f"🔑 First key preview: {api_keys[0][:20]}...")
    
    # Test Gemini configuration
    try:
        genai.configure(api_key=api_keys[0])
        model = genai.GenerativeModel('gemini-1.5-flash')
        print("✅ Gemini client configured successfully")
        
        # Test Arabic description generation
        print("\n🤖 Testing Arabic description generation...")
        
        mod_name = "Dragon Craft Ultimate"
        mod_category = "Addons"
        manual_features = """- يضيف 5 أنواع مختلفة من التنانين
- تنانين قابلة للترويض والركوب
- أسلحة ودروع التنانين الأسطورية
- كهوف التنانين مع كنوز نادرة
- تأثيرات بصرية مذهلة للنار والطيران
- متوافق مع Minecraft PE 1.20+"""

        prompt = f"""
أنت كاتب محتوى ماين كرافت خبير باللغة العربية. مهمتك هي كتابة وصف جذاب وفريد ومفصل 
(حوالي 100-200 كلمة) لمحتوى ماين كرافت باللغة العربية، مع الحفاظ على نبرة طبيعية وبشرية.

**اسم المحتوى:** {mod_name}
**نوع المحتوى:** {mod_category}

**معلومات المصدر:**
الميزات المقدمة من المستخدم:
\"\"\"{manual_features}\"\"\"

**التعليمات:**
- استخدم المعلومات المقدمة كأساس رئيسي لكتابتك
- أنشئ وصفاً جديداً وفريداً وجذاباً
- اذكر الميزات الرئيسية للمود صراحة
- سلط الضوء على قيمة المحتوى للاعبين
- استخدم اللغة العربية الطبيعية والسلسة
- تجنب التكرار والنسخ المباشر
- يجب أن يكون الناتج باللغة العربية فقط
- لا تضع اسم المحتوى أو النوع في البداية إلا إذا كان ذلك مناسباً طبيعياً
- لا تضف أي عناوين مثل "الوصف:" أو ما شابه؛ قدم النص الوصفي فقط

قدم إجابتك بهذا التنسيق بالضبط:
[ARABIC_DESCRIPTION]
وصفك الجذاب باللغة العربية هنا...
[/ARABIC_DESCRIPTION]
"""
        
        response = model.generate_content(prompt)
        full_response = response.text.strip()
        print(f"✅ Gemini response received: {len(full_response)} characters")
        
        # Extract Arabic description
        import re
        desc_match = re.search(r'\[ARABIC_DESCRIPTION\](.*?)\[/ARABIC_DESCRIPTION\]', full_response, re.DOTALL)
        if desc_match:
            arabic_desc = desc_match.group(1).strip()
            print(f"✅ Arabic description extracted successfully:")
            print("="*50)
            print(arabic_desc)
            print("="*50)
        else:
            print(f"⚠️ Could not extract description from response. Full response:")
            print("="*50)
            print(full_response)
            print("="*50)
            
    except Exception as e:
        print(f"❌ Gemini test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
else:
    if not api_keys:
        print("❌ No API keys found")
    if not GEMINI_AVAILABLE:
        print("❌ Gemini library not available")

print("\n" + "="*50)
print("ARABIC FEATURE TEST COMPLETE")
print("="*50)
