"""
كلاس استخراج بيانات مودات mcpedl.com
"""
import requests
import time
import logging
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin

from config import Config
from utils import (
    clean_text, extract_file_size, extract_version_numbers,
    make_absolute_url, extract_social_platform, safe_int_convert,
    delay_request, validate_image_url, is_valid_mcpedl_url
)

class MCPEDLScraper:
    """كلاس استخراج بيانات مودات من mcpedl.com"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()
        self.session.headers.update(Config.HEADERS)

    def fetch_page(self, url: str) -> Optional[BeautifulSoup]:
        """جلب وتحليل صفحة الويب"""
        if not is_valid_mcpedl_url(url):
            self.logger.error(f"رابط غير صحيح: {url}")
            return None

        for attempt in range(Config.MAX_RETRIES):
            try:
                self.logger.info(f"جلب الصفحة: {url} (المحاولة {attempt + 1})")

                response = self.session.get(
                    url,
                    timeout=Config.REQUEST_TIMEOUT,
                    allow_redirects=True
                )
                response.raise_for_status()

                # التحقق من نوع المحتوى
                content_type = response.headers.get('content-type', '')
                if 'text/html' not in content_type:
                    self.logger.error(f"نوع محتوى غير متوقع: {content_type}")
                    return None

                soup = BeautifulSoup(response.text, 'html.parser')
                self.logger.info("تم جلب وتحليل الصفحة بنجاح")

                # تأخير بين الطلبات
                if attempt < Config.MAX_RETRIES - 1:
                    delay_request(Config.REQUEST_DELAY)

                return soup

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"خطأ في المحاولة {attempt + 1}: {e}")
                if attempt < Config.MAX_RETRIES - 1:
                    time.sleep(2 ** attempt)  # تأخير متزايد
                else:
                    self.logger.error(f"فشل في جلب الصفحة بعد {Config.MAX_RETRIES} محاولات")

        return None

    def extract_title(self, soup: BeautifulSoup) -> str:
        """استخراج عنوان المود"""
        selectors = Config.SELECTORS['title'].split(', ')

        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                title = clean_text(element.get_text())
                if title:
                    self.logger.info(f"تم استخراج العنوان: {title}")
                    return title

        # محاولة استخراج من title tag
        title_tag = soup.find('title')
        if title_tag:
            title = clean_text(title_tag.get_text())
            # إزالة اسم الموقع من العنوان
            title = title.replace(' - MCPEDL', '').replace(' | MCPEDL', '')
            if title:
                return title

        self.logger.warning("لم يتم العثور على عنوان المود")
        return ""

    def extract_description(self, soup: BeautifulSoup) -> str:
        """استخراج وصف المود"""
        selectors = Config.SELECTORS['description'].split(', ')
        description_parts = []

        for selector in selectors:
            elements = soup.select(selector)
            for element in elements[:5]:  # أول 5 فقرات فقط
                text = clean_text(element.get_text())
                if text and len(text) > 20:  # تجاهل النصوص القصيرة جداً
                    description_parts.append(text)

        description = ' '.join(description_parts)
        if description:
            self.logger.info(f"تم استخراج الوصف: {len(description)} حرف")
            return description[:2000]  # حد أقصى للطول

        self.logger.warning("لم يتم العثور على وصف المود")
        return ""

    def extract_category(self, soup: BeautifulSoup) -> str:
        """استخراج فئة المود"""
        # محاولة استخراج من breadcrumbs
        breadcrumbs = soup.select('.breadcrumbs a, .breadcrumb a')
        if breadcrumbs and len(breadcrumbs) > 1:
            category = clean_text(breadcrumbs[-2].get_text())
            if category and category.lower() not in ['home', 'mcpedl']:
                return category

        # محاولة استخراج من URL
        selectors = Config.SELECTORS['category'].split(', ')
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                category = clean_text(element.get_text())
                if category:
                    return category

        return "Mods"  # فئة افتراضية

    def extract_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """استخراج روابط الصور"""
        image_urls = []

        # الصورة الرئيسية
        main_img_selectors = Config.SELECTORS['main_image'].split(', ')
        for selector in main_img_selectors:
            img = soup.select_one(selector)
            if img and img.get('src'):
                url = make_absolute_url(base_url, img['src'])
                if validate_image_url(url):
                    image_urls.append(url)
                    break

        # صور المعرض
        gallery_selectors = Config.SELECTORS['gallery_images'].split(', ')
        for selector in gallery_selectors:
            images = soup.select(selector)
            for img in images[:10]:  # حد أقصى 10 صور
                src = img.get('src') or img.get('data-src')
                if src:
                    url = make_absolute_url(base_url, src)
                    if validate_image_url(url) and url not in image_urls:
                        image_urls.append(url)

        self.logger.info(f"تم استخراج {len(image_urls)} صورة")
        return image_urls

    def extract_versions(self, soup: BeautifulSoup) -> str:
        """استخراج إصدارات Minecraft المدعومة"""
        version_selectors = Config.SELECTORS['version'].split(', ')
        all_versions = []

        for selector in version_selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text()
                versions = extract_version_numbers(text)
                all_versions.extend(versions)

        # البحث في النص العام للصفحة
        page_text = soup.get_text()
        page_versions = extract_version_numbers(page_text)
        all_versions.extend(page_versions)

        # إزالة التكرارات والترتيب
        unique_versions = sorted(list(set(all_versions)), reverse=True)

        if unique_versions:
            versions_str = ', '.join(unique_versions[:5])  # أول 5 إصدارات
            self.logger.info(f"تم استخراج الإصدارات: {versions_str}")
            return versions_str

        return "1.20+"  # إصدار افتراضي

    def extract_download_info(self, soup: BeautifulSoup, base_url: str) -> Dict[str, Any]:
        """استخراج معلومات التحميل"""
        download_info = {
            'download_url': '',
            'size': ''
        }

        # البحث عن رابط التحميل
        download_selectors = Config.SELECTORS['download_link'].split(', ')
        for selector in download_selectors:
            link = soup.select_one(selector)
            if link and link.get('href'):
                download_url = make_absolute_url(base_url, link['href'])
                download_info['download_url'] = download_url
                break

        # البحث عن حجم الملف
        size_selectors = Config.SELECTORS['file_size'].split(', ')
        for selector in size_selectors:
            element = soup.select_one(selector)
            if element:
                size = extract_file_size(element.get_text())
                if size:
                    download_info['size'] = size
                    break

        return download_info

    def extract_creator_info(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """استخراج معلومات المطور"""
        creator_info = {
            'creator_name': '',
            'creator_contact_info': '',
            'creator_social_channels': []
        }

        # اسم المطور
        creator_selectors = Config.SELECTORS['creator'].split(', ')
        for selector in creator_selectors:
            element = soup.select_one(selector)
            if element:
                creator_name = clean_text(element.get_text())
                if creator_name:
                    creator_info['creator_name'] = creator_name
                    break

        # روابط التواصل الاجتماعي
        social_selectors = Config.SELECTORS['social_links'].split(', ')
        for selector in social_selectors:
            links = soup.select(selector)
            for link in links:
                href = link.get('href')
                if href and any(platform in href.lower() for platform in
                              ['youtube', 'discord', 'twitter', 'instagram', 'facebook', 'github']):
                    platform = extract_social_platform(href)
                    creator_info['creator_social_channels'].append({
                        'platform': platform,
                        'url': href
                    })

        return creator_info

    def extract_stats(self, soup: BeautifulSoup) -> Dict[str, int]:
        """استخراج إحصائيات المود"""
        stats = {
            'downloads': 0,
            'likes': 0
        }

        # عدد التحميلات
        downloads_selectors = Config.SELECTORS['downloads_count'].split(', ')
        for selector in downloads_selectors:
            element = soup.select_one(selector)
            if element:
                downloads = safe_int_convert(element.get_text())
                if downloads > 0:
                    stats['downloads'] = downloads
                    break

        # عدد الإعجابات
        likes_selectors = Config.SELECTORS['likes_count'].split(', ')
        for selector in likes_selectors:
            element = soup.select_one(selector)
            if element:
                likes = safe_int_convert(element.get_text())
                if likes > 0:
                    stats['likes'] = likes
                    break

        return stats

    def scrape_mod_data(self, url: str) -> Optional[Dict[str, Any]]:
        """استخراج جميع بيانات المود من الرابط"""
        self.logger.info(f"بدء استخراج بيانات المود من: {url}")

        # جلب الصفحة
        soup = self.fetch_page(url)
        if not soup:
            return None

        try:
            # استخراج البيانات الأساسية
            mod_data = {
                'name': self.extract_title(soup),
                'description': self.extract_description(soup),
                'category': self.extract_category(soup),
                'image_urls': self.extract_images(soup, url),
                'version': self.extract_versions(soup),
                'source_url': url
            }

            # معلومات التحميل
            download_info = self.extract_download_info(soup, url)
            mod_data.update(download_info)

            # معلومات المطور
            creator_info = self.extract_creator_info(soup)
            mod_data.update(creator_info)

            # الإحصائيات
            stats = self.extract_stats(soup)
            mod_data.update(stats)

            # التحقق من البيانات الأساسية
            if not mod_data['name']:
                self.logger.error("لم يتم العثور على اسم المود")
                return None

            self.logger.info(f"تم استخراج بيانات المود بنجاح: {mod_data['name']}")
            return mod_data

        except Exception as e:
            self.logger.error(f"خطأ في استخراج بيانات المود: {e}")
            return None

    def close(self):
        """إغلاق الجلسة"""
        self.session.close()
