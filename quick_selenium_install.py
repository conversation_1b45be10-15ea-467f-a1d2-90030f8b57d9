#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تثبيت سريع لـ Selenium لحل مشكلة MCPEDL
"""

import subprocess
import sys

def install_selenium():
    """تثبيت Selenium وwebdriver-manager"""
    print("🚀 تثبيت Selenium وwebdriver-manager...")
    
    packages = [
        "selenium",
        "webdriver-manager"
    ]
    
    for package in packages:
        try:
            print(f"📦 تثبيت {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ تم تثبيت {package} بنجاح")
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في تثبيت {package}: {e}")
            return False
    
    return True

def test_selenium():
    """اختبار Selenium"""
    print("\n🧪 اختبار Selenium...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
        
        print("✅ تم استيراد Selenium بنجاح")
        
        # إعداد Chrome
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        # إنشاء driver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # اختبار بسيط
        driver.get("https://www.google.com")
        title = driver.title
        print(f"✅ اختبار نجح: {title}")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Selenium: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إعداد سريع لـ Selenium")
    print("=" * 40)
    
    # تثبيت
    if not install_selenium():
        print("❌ فشل في التثبيت")
        return False
    
    # اختبار
    if not test_selenium():
        print("❌ فشل في الاختبار")
        return False
    
    print("\n🎉 تم إعداد Selenium بنجاح!")
    print("💡 الآن يمكنك تجربة استخراج MCPEDL مرة أخرى")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ جاهز للاستخدام!")
        else:
            print("\n❌ يحتاج إصلاح يدوي")
        
        input("\nاضغط Enter للخروج...")
        
    except KeyboardInterrupt:
        print("\n⏹️ تم الإيقاف")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        input("\nاضغط Enter للخروج...")
