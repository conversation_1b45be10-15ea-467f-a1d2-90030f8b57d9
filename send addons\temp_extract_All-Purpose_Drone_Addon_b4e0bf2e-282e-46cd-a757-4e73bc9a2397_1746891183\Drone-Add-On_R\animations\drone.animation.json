{"format_version": "1.8.0", "animations": {"animation.drone.float": {"loop": true, "animation_length": 2, "bones": {"drone": {"position": {"0.0": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 2, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 3, 0], "lerp_mode": "catmullrom"}, "2.0": {"post": [0, -1, 0], "lerp_mode": "catmullrom"}}}}}, "animation.drone.propulsion": {"loop": true, "animation_length": 0.75, "particle_effects": {"0.0": {"effect": "propulsion", "locator": "propulsion"}}}, "animation.drone.thruster": {"loop": true, "animation_length": 0.01, "bones": {"thruster": {"rotation": [22.5, 0, 0]}}}, "animation.drone.inclination": {"loop": true, "animation_length": 0.01, "bones": {"drone": {"rotation": [15, 0, 0]}, "bone7": {"rotation": [0, 0, -15]}, "bone9": {"rotation": [0, 0, 20]}, "bone3": {"rotation": [0, 0, 15]}, "bone5": {"rotation": [0, 0, -20]}, "bone15": {"rotation": [0, 0, -15]}, "bone17": {"rotation": [0, 0, 20]}, "bone11": {"rotation": [0, 0, 15]}, "bone13": {"rotation": [0, 0, -20]}}}, "animation.drone.look_at_target": {"loop": true, "bones": {"head": {"relative_to": {"rotation": "entity"}, "rotation": ["q.variant ? 0 : v.targetX - this >= 25 ? 25 : v.targetX - this", "v.targetY - this", 0]}}}, "animation.drone.eye": {"loop": true, "bones": {"pupil": {"position": ["v.targetY > 0.1 ? -0.6 : v.targetY < -0.1 ? 0.6 : 0", "v.targetX > 15 ? -0.5 : v.targetX < -15 ? 0.5 : 0", 0]}}}, "animation.drone.lights": {"loop": true, "animation_length": 1, "bones": {"light_red_off": {"scale": {"0.0": [0, 0, 0], "0.5": {"pre": [0, 0, 0], "post": [1, 1, 1]}}}, "light_red": {"scale": {"0.0": [1, 1, 1], "0.5": {"pre": [1, 1, 1], "post": [0, 0, 0]}}}}}, "animation.drone.walk": {"loop": true, "animation_length": 0.5, "bones": {"claw_piv2": {"rotation": {"0.0": {"post": [0, -20, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 20, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -20, 0], "lerp_mode": "catmullrom"}}}, "bone7": {"rotation": {"0.25": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.3333": {"post": [0, 0, 15], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 0, 15], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "claw_piv3": {"rotation": {"0.0": {"post": [0, -20, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 20, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, -20, 0], "lerp_mode": "catmullrom"}}}, "bone3": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0833": {"post": [0, 0, -15], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 0, -15], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "claw_piv4": {"rotation": {"0.0": {"post": [0, 20, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, -20, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 20, 0], "lerp_mode": "catmullrom"}}}, "bone11": {"rotation": {"0.25": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.3333": {"post": [0, 0, -15], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 0, -15], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "claw_piv5": {"rotation": {"0.0": {"post": [0, 20, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, -20, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 20, 0], "lerp_mode": "catmullrom"}}}, "bone15": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.0833": {"post": [0, 0, 15], "lerp_mode": "catmullrom"}, "0.1667": {"post": [0, 0, 15], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.drone.walk_mode": {"loop": true, "bones": {"drone": {"position": [0, -2, 0]}, "claw_0": {"position": [0, -3, 0]}, "claw_1": {"position": [0, -3, 0]}, "claw_3": {"position": [0, -3, 0]}, "claw_4": {"position": [0, -3, 0]}, "claw_piv2": {"rotation": [0, 0, 32.5]}, "bone7": {"rotation": [0, 0, 30]}, "bone9": {"rotation": [0, 0, -12.5]}, "claw_piv3": {"rotation": [0, 0, -32.5]}, "bone3": {"rotation": [0, 0, -30]}, "bone5": {"rotation": [0, 0, 12.5]}, "claw_piv4": {"rotation": [0, 0, -32.5]}, "bone11": {"rotation": [0, 0, -30]}, "bone13": {"rotation": [0, 0, 12.5]}, "claw_piv5": {"rotation": [0, 0, 32.5]}, "bone15": {"rotation": [0, 0, 30]}, "bone17": {"rotation": [0, 0, -12.5]}}}, "animation.drone.drill_mode": {"loop": true, "animation_length": 0.5, "bones": {"drone": {"rotation": {"0.0": {"post": [0, 5, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, -5, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 5, 0], "lerp_mode": "catmullrom"}}}, "claw_0": {"position": [0, -3, 0]}, "claw_piv2": {"rotation": [54.83681, -58.83448, 30.2753]}, "bone7": {"rotation": {"0.0": {"post": [0, 0, 50], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, -15], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 50], "lerp_mode": "catmullrom"}}}, "bone9": {"rotation": {"0.0": {"post": [0, 0, 30], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 30], "lerp_mode": "catmullrom"}}}, "claw_1": {"position": [0, -3, 0]}, "claw_piv3": {"rotation": [54.83681, 58.83448, -30.2753]}, "bone3": {"rotation": {"0.0": {"post": [0, 0, 15], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, -50], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 15], "lerp_mode": "catmullrom"}}}, "bone5": {"rotation": {"0.0": {"post": [0, 0, 20], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, -10], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 20], "lerp_mode": "catmullrom"}}}}}, "animation.drone.wait": {"loop": true, "animation_length": 1, "bones": {"drone": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 5], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, 0, -5], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [-3, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [3, 0, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.drone.in_water": {"loop": true, "animation_length": 0.5, "bones": {"claws": {"rotation": {"0.0": [0, 0, 0], "0.5": [0, 180, 0]}}}}, "animation.drone.bubbles": {"loop": true, "animation_length": 1.25, "particle_effects": {"0.0": {"effect": "water_propulsion", "locator": "propulsion"}}}, "animation.drone.holding": {"loop": true, "bones": {"bone7": {"rotation": [0, 0, -17.5]}, "bone3": {"rotation": [0, 0, 17.5]}, "bone11": {"rotation": [0, 0, 17.5]}, "bone15": {"rotation": [0, 0, -17.5]}}}, "animation.drone.walk_wait": {"loop": true, "animation_length": 1, "bones": {"main_head": {"position": {"0.0": [0, 0, 0], "0.5": [0, 2, 0], "1.0": [0, 0, 0]}}, "claw_0": {"rotation": {"0.0": [0, 0, 0], "0.5": [0, 0, -15], "1.0": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.5": [0, 2, 0], "1.0": [0, 0, 0]}}, "claw_1": {"rotation": {"0.0": [0, 0, 0], "0.5": [0, 0, 15], "1.0": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.5": [0, 2, 0], "1.0": [0, 0, 0]}}, "claw_3": {"rotation": {"0.0": [0, 0, 0], "0.5": [0, 0, -15], "1.0": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.5": [0, 2, 0], "1.0": [0, 0, 0]}}, "claw_4": {"rotation": {"0.0": [0, 0, 0], "0.5": [0, 0, 15], "1.0": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.5": [0, 2, 0], "1.0": [0, 0, 0]}}, "thruster": {"position": {"0.0": [0, 0, 0], "0.5": [0, 2, 0], "1.0": [0, 0, 0]}}}}, "animation.drone.hide": {"loop": true, "animation_length": 0.1, "bones": {"claws": {"scale": 0}, "antenna": {"scale": 0}, "thruster": {"position": [0, 3000, 0], "scale": 0}, "eye": {"scale": 0}, "main_head": {"scale": 0}}}, "animation.drone.deny": {"loop": true, "animation_length": 1, "bones": {"head": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [0, 25, 0], "lerp_mode": "catmullrom"}, "0.4": {"post": [0, -25, 0], "lerp_mode": "catmullrom"}, "0.6": {"post": [0, 25, 0], "lerp_mode": "catmullrom"}, "0.8": {"post": [0, -25, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.drone.walk_deny": {"loop": true, "animation_length": 1, "bones": {"main_head": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.2": {"post": [0, 25, 0], "lerp_mode": "catmullrom"}, "0.4": {"post": [0, -25, 0], "lerp_mode": "catmullrom"}, "0.6": {"post": [0, 25, 0], "lerp_mode": "catmullrom"}, "0.8": {"post": [0, -25, 0], "lerp_mode": "catmullrom"}, "1.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}}}, "animation.drone.panel_on": {"loop": "hold_on_last_frame", "animation_length": 1, "bones": {"antenna": {"position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [0, 0, 2], "lerp_mode": "catmullrom"}, "0.3333": [0, 0, 2]}}, "panel": {"position": {"0.2083": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 4, 0], "lerp_mode": "catmullrom"}, "0.5417": [0, 4, 0]}}, "part_1": {"rotation": {"0.4167": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.625": {"post": [0, 0, 45], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0, 90], "lerp_mode": "catmullrom"}, "0.9583": [0, 0, 90]}}, "part_5": {"rotation": {"0.625": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.7917": {"post": [0, 0, -15], "lerp_mode": "catmullrom"}, "0.9167": [0, 0, -15]}}, "part_2": {"rotation": {"0.4167": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.625": {"post": [0, 0, -45], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0, -90], "lerp_mode": "catmullrom"}, "0.9583": [0, 0, -90]}}, "part_3": {"rotation": {"0.625": {"pre": [0, 0, 0], "post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.7917": {"post": [0, 0, 15], "lerp_mode": "catmullrom"}, "0.9167": [0, 0, 15]}}}}, "animation.drone.panel_off": {"animation_length": 1, "bones": {"antenna": {"position": {"0.5833": {"pre": [0, 0, 2], "post": [0, 0, 2], "lerp_mode": "catmullrom"}, "0.8333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.9583": [0, 0, 0]}}, "panel": {"position": {"0.4167": {"pre": [0, 4, 0], "post": [0, 4, 0], "lerp_mode": "catmullrom"}, "0.625": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.75": [0, 0, 0]}}, "part_1": {"rotation": {"0.0": {"post": [0, 0, 90], "lerp_mode": "catmullrom"}, "0.2083": {"post": [0, 0, 45], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5417": [0, 0, 0]}}, "part_5": {"rotation": {"0.0417": {"pre": [0, 0, -15], "post": [0, 0, -15], "lerp_mode": "catmullrom"}, "0.2083": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.3333": [0, 0, 0]}}, "part_2": {"rotation": {"0.0": {"post": [0, 0, -90], "lerp_mode": "catmullrom"}, "0.2083": {"post": [0, 0, -45], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.5417": [0, 0, 0]}}, "part_3": {"rotation": {"0.0417": {"pre": [0, 0, 15], "post": [0, 0, 15], "lerp_mode": "catmullrom"}, "0.2083": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "0.3333": [0, 0, 0]}}}}, "animation.drone.destroyed": {"loop": "hold_on_last_frame", "animation_length": 0.0833, "bones": {"drone": {"scale": {"0.0": [1, 1, 1], "0.0833": [1.2, 1.2, 1.2]}}}}}}