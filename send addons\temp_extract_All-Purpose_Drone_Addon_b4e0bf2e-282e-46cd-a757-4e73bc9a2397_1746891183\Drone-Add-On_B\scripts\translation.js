import { ModalFormData } from "@minecraft/server-ui"
import { kUI } from "./mainSystem"

export const langs = [ "English", "Español", "Português", "中文(简体)" ]

export function confLang(player, i, p, rFunct, dron) {
    const { dLang } = JSON.parse(player.getTags().find(f => { return f.includes('{"dLang":') }) ?? '{"dLang":0}')
    const menu = new ModalFormData().title(`${kUI}§g§fLanguage`).dropdown("", langs, dLang)
    menu.show(player).then(r => {
        if (!r.canceled) {
            player.removeTag(`{"dLang":${dLang}}`)
            player.addTag(JSON.stringify({ dLang: r.formValues[0] }))
            if (dron) {
                const { dLang: lang } = JSON.parse(dron.getTags().find(f => { return f.includes('{"dLang":') }) ?? '{"dLang":0}')
                dron.removeTag(`{"dLang":${lang}}`)
                dron.addTag(JSON.stringify({ dLang: r.formValues[0] }))
            }
            rFunct(i, p)
        }
    })
}

export function translation(lang, text) {
    let words = english
    switch (lang) {
        case 0: words = english; break
        case 1: words = spanish; break
        case 2: words = portuguese; break
        case 3: words = chinese; break
    }
    return words[text] ?? "§kUndefined"
}

const english = {
    langu: "Language",
    apd: "All-Purpose Drone",
    batt: "Battery",
    cTask: "Current task",
    tasks: "Tasks",
    sTask: "Simple Tasks",
    lock: "Locked",
    fMode: "Fly Mode",
    wMode: "Walk Mode",
    fplay: "Follow player",
    uplay: "Unfollow player",
    dCam: "Drone camera",
    setts: "Settings",
    clTask: "Cancel task",
    desDro: "Destroy drone",
    yes: "Yes",
    noo: "No ",
    dTasks: "Drone Tasks",
    mALoc: "Mine a location",
    kllMb: "Kill mobs",
    fALoc: "fill a location",
    bMobs: "Bring mobs",
    mNBlo: "Mine nearby blocks",
    cItem: "Collect items",
    oDect: "Ore detect",
    mItem: "Move items",
    cStru: "Clone structure",
    fishg: "Fishing",
    iArea: "Illuminate area",
    rCatt: "Raise cattle",
    sShee: "Shear sheeps",
    mCows: "Milking cows",
    lwarn: "Lag warning",
    cBatt: "Charge battery",
    gTLoc: "Go to location",
    sInve: "Save inventory",
    tIFCo: "Take items from container",
    dInve: "Drop inventory",
    sTCam: "Sneak to close the camera",
    aStts: "Antenna settings",
    wOwnr: "Without owner",
    swarm: "Swarm",
    sStts: "Swarm Settings",
    crLoc: "Charge and rest location",
    gRLoc: "Go to rest location when finishing a task",
    dName: "Drone name",
    lcSSt: "Locked for swarm settings",
    sONam: "Show owner name",
    vName: "Visible in the name",
    sBPer: "Show battery percentage",
    nVisy: "Name Visibility:\n§8To switch between always show and partially show you must restart the world for it to take effect",
    ttPla: "Teleport to the player\n§8By following the player",
    mDttP: "Minimum distance to teleport to the player",
    dOwne: "Drone owner",
    aFMod: "Auto fly mode\n§8The drone will activate fly mode if its battery is more than 10 percent",
    tSttF: "These are the settings for the",
    dIfSt: "drone, if you save them all the drones in this swarm will have the same settings",
    dca: "Drone Control Antenna",
    drc: "Drone remote control",
    cDSwa: "Create Drone Swarm",
    drones: "Drones",
    dofr: "Drones out of range",
    ndws: "Nearby drones without swarm",
    addd: "Add Drone",
    tandn: "There are no drones nearby",
    add: "Add",
    ndbts: "Nearby drones belonging to this swarm",
    remov: "Remove",
    tndts: "There are no drones in this swarm",
    dOpts: "Drone options",
    seleD: "Select drone",
    deleD: "Delete Drone",
    deleS: "Delete Swarm",
    dSwrm: "Drone Swarm",
    swarms: "Swarms",
    clTsks: "Cancel tasks",
    sName: "Swarm name",
    selfD: "Self Destruct",
    none: "§cNone",
    difT: "§cDifferent tasks",
    dnotS: "Do not show",
    parSh: "Partially show",
    alwSh: "Always show",
    asr: "Antenna Scanning Radio",
    eses: "Emit signal every - Seconds",
    antNa: "Antenna name",
    nVisi: "Name Visibility",
    aOwnr: "Antenna owner",
    ldTks: "Locked during a task",
    onlin: "Online",
    to: "to",
    from: "from",
    optio: "Optional",
    llorm: "Lag in large or medium areas",
    scitw: "Select coordinates in the world\n§8All settings will be ignored.",
    bfeqb: "Block filter\n §8Empty equals all blocks",
    dtcrh: "Harvest mode\n§8The drone will take the crops ready to be harvested.",
    sabsa: "Reload task\n§8It will search again for new blocks in the selected area",
    ciwbs: "Save items in\n§8Chest where items will be stored",
    noSel: "§Nnot selected",
    mobfl: "Mob Filter",
    animl: "Animals",
    monst: "Monsters",
    wtcre: "Underwater creatures",
    fMobs: "flying creatures",
    acree: "Avoid creepers",
    itmob: "Ignore tamed mobs",
    src: "Search radius center",
    srBks: "Search radius §8-§r Blocks",
    fptss: "follow player\n§8The previous two settings will be ignored and these will be applied\nSearch radius center: player location\nSearch Radius - Blocks: 8",
    bccds: "Block container\n§8Chest where the drone will look for blocks to supply itself.",
    cmdps: "Cultivation mode\n§8The drone will place seeds and others on farmlands",
    rtsas: "Reload task\n§8It will again search for space to place blocks in the selected area",
    lmbin: "Leave mobs in",
    msr: "Min search radius",
    mxsr: "Max search radius",
    mfwbi: "Mob filter will be ignored",
    hsrcl: "High scan radii can cause lag.\n§7Horizontal scan radius §8- §7Blocks:",
    vsrbs: "Vertical scan radius - Blocks:",
    fpifv: "follow player\n§8If you leave this option disabled, the drone will wander around the world looking for valid blocks to mine.",
    rdbls: "Radius - Blocks",
    fppsr: "follow player\n§8The previous settings will be ignored and these will be applied\nSearch radius center: drone location\nRadius - Blocks: 8",
    ifeei: "Item filter\n §8Empty equals all items",
    vOre: "Vanilla Ore",
    iore: "Iron Ore",
    gore: "Gold Ore",
    dore: "Diamond Ore",
    llore: "Lapis Lazuli Ore",
    rore: "Redstone Ore",
    core: "Coal Ore",
    cpore: "Copper Ore",
    eore: "Emerald Ore",
    qore: "Quartz Ore",
    adore: "Ancient Debris",
    ctOre: "Custom Ore",
    oabck: "Only a block containing the keywords \n§8\"_ore, _ore_ ore_\" in its identifier is allowed.",
    fptse: "Follow player §c= §aTrue\n\n§7Sound effect",
    contr: "Container",
    ttcsa: "The task was cancelled, submit to save again",
    cgrdi: "Charging radius",
    coord: "Coordinates",
    cdSel: "Canceled selection",
    iwdas: "Interact with the drone to §aaccept§7 the selection. \nBreak a block to §ccancel §7the selection.",
    selMd: "Selection mode",
    bbtsl: "Break a block to select.",
    anten: "Antenna",
    owner: "Owner",
    sktrp: "Sneak to repair",
    doutl: "Drone Outline",
    outCo: "Outline color",
    cusCo: "Custom Color",
    black: "Black",
    white: "White",
    red: "Red",
    blue: "Blue",
    yellw: "Yellow",
    green: "Green",
    purpl: "Purple",
    orang: "Orange",
    pink: "Pink",
    dBlue: "Drone blue",
    xpOrb: "xp orb",
    tDete: "Target detected",
    occlr: "Outline Custom Color",
    remlq: "Remove liquids",
    tlwsk: "Teleport when stuck",
    empty: "empty",
}

const spanish = {
    langu: "Idioma",
    apd: "Dron Multiusos",
    batt: "Batería",
    cTask: "Tarea actual",
    tasks: "Tareas",
    sTask: "Tareas simples",
    lock: "Bloqueado",
    fMode: "Modo vuelo",
    wMode: "Modo caminar",
    fplay: "Seguir jugador",
    uplay: "Dejar de seguir jugador",
    dCam: "Cámara del dron",
    setts: "Configuración",
    clTask: "Cancelar tarea",
    desDro: "Destruir dron",
    yes: "Sí",
    noo: "No   ",
    dTasks: "Tareas del dron",
    mALoc: "Minar una ubicación",
    kllMb: "Matar mobs",
    fALoc: "Rellenar una ubicación",
    bMobs: "Traer mobs",
    mNBlo: "Minar bloques cercanos",
    cItem: "Recoger objetos",
    oDect: "Detector de minerales",
    mItem: "Mover objetos",
    cStru: "Clonar estructura",
    fishg: "Pescar",
    iArea: "Iluminar área",
    rCatt: "Criar ganado",
    sShee: "Esquilar ovejas",
    mCows: "Ordeñar vacas",
    lwarn: "Advertencia de lag",
    cBatt: "Cargar batería",
    gTLoc: "Ir a la ubicación",
    sInve: "Guardar inventario",
    tIFCo: "Tomar objetos del contenedor",
    dInve: "Soltar inventario",
    sTCam: "Agacharse para cerrar la cámara",
    aStts: "Configuración de la antena",
    wOwnr: "Sin dueño",
    swarm: "Enjambre",
    sStts: "Configuración del enjambre",
    crLoc: "Ubicación de carga y descanso",
    gRLoc: "Ir a la ubicación de descanso al finalizar una tarea",
    dName: "Nombre del dron",
    lcSSt: "Bloqueado para la configuración del enjambre",
    sONam: "Mostrar nombre del dueño",
    vName: "Visible en el nombre",
    sBPer: "Mostrar porcentaje de batería",
    nVisy: "Visibilidad del nombre:\n§8Para alternar entre siempre visible y parcialmente visible, debes reiniciar el mundo para que tenga efecto",
    ttPla: "Teletransportarse al jugador\n§8Siguiendo al jugador",
    mDttP: "Distancia mínima para teletransportarse al jugador",
    dOwne: "Dueño del dron",
    aFMod: "Modo de vuelo automático\n§8El dron activará el modo vuelo si su batería es superior al 10 por ciento",
    tSttF: "Estas son las configuraciones del",
    dIfSt: "dron, si las guardas, todos los drones en este enjambre tendrán la misma configuración",
    dca: "Antena de Control de Drones",
    drc: "Control remoto del dron",
    cDSwa: "Crear enjambre de drones",
    drones: "Drones",
    dofr: "Drones fuera de alcance",
    ndws: "Drones cercanos sin enjambre",
    addd: "Agregar dron",
    tandn: "No hay drones cercanos",
    add: "Agregar",
    ndbts: "Drones cercanos pertenecientes a este enjambre",
    remov: "Eliminar",
    tndts: "No hay drones en este enjambre",
    dOpts: "Opciones del dron",
    seleD: "Seleccionar dron",
    deleD: "Eliminar dron",
    deleS: "Eliminar enjambre",
    dSwrm: "Enjambre de drones",
    swarms: "Enjambres",
    clTsks: "Cancelar tareas",
    sName: "Nombre del enjambre",
    selfD: "Autodestrucción",
    none: "§cNinguna",
    difT: "§cTareas diferentes",
    dnotS: "No mostrar",
    parSh: "Mostrar parcialmente",
    alwSh: "Mostrar siempre",
    asr: "Radio de Escaneo de Antena",
    eses: "Emitir señal cada - segundos",
    antNa: "Nombre de la antena",
    nVisi: "Visibilidad del nombre",
    aOwnr: "Dueño de la antena",
    ldTks: "Bloqueado durante una tarea",
    onlin: "En línea",
    to: "A",
    from: "De",
    optio: "Opcional",
    llorm: "Lag en áreas grandes o medianas",
    scitw: "Seleccionar coordenadas en el mundo\n§8Todas las configuraciones serán ignoradas.",
    bfeqb: "Filtro de bloques\n §8Vacío equivale a todos los bloques",
    dtcrh: "Modo cosecha\n§8El dron tomará los cultivos listos para ser cosechados.",
    sabsa: "Recargar tarea\n§8Buscará nuevamente bloques en el área seleccionada",
    ciwbs: "Guardar objetos en\n§8Cofre donde se almacenarán los objetos",
    noSel: "§Nno seleccionado",
    mobfl: "Filtro de Mobs",
    animl: "Animales",
    monst: "Monstruos",
    wtcre: "Criaturas acuáticas",
    fMobs: "Criaturas voladoras",
    acree: "Evitar creepers",
    itmob: "Ignorar mobs domesticados",
    src: "Centro del radio de búsqueda",
    srBks: "Radio de búsqueda §8-§r Bloques",
    fptss: "Seguir jugador\n§8Las dos configuraciones anteriores serán ignoradas y estas se aplicarán\nCentro del radio de búsqueda: ubicación del jugador\nRadio de búsqueda - Bloques: 8",
    bccds: "Contenedor de bloques\n§8Cofre donde el dron buscará bloques para abastecerse.",
    cmdps: "Modo cultivo\n§8El dron colocará semillas y otros en tierras de cultivo.",
    rtsas: "Recargar tarea\n§8Buscará nuevamente espacio para colocar bloques en el área seleccionada.",
    lmbin: "Dejar mobs en",
    msr: "Radio de búsqueda mínimo",
    mxsr: "Radio de búsqueda máximo",
    mfwbi: "El filtro de mobs será ignorado",
    hsrcl: "Un radio de escaneo alto puede causar lag.\n§7Radio de escaneo horizontal §8- §7Bloques:",
    vsrbs: "Radio de escaneo vertical - Bloques:",
    fpifv: "Seguir jugador\n§8Si dejas esta opción deshabilitada, el dron vagará por el mundo buscando bloques válidos para minar.",
    rdbls: "Radio - Bloques",
    fppsr: "Seguir jugador\n§8Se ignorarán las configuraciones anteriores y se aplicarán estas\nCentro del radio de búsqueda: ubicación del dron\nRadio - Bloques: 8",
    ifeei: "Filtro de ítems\n §8Vacío equivale a todos los ítems",
    vOre: "Mineral Vanilla",
    iore: "Mineral de hierro",
    gore: "Mineral de oro",
    dore: "Mineral de diamante",
    llore: "Mineral de lapislázuli",
    rore: "Mineral de redstone",
    core: "Mineral de carbón",
    cpore: "Mineral de cobre",
    eore: "Mineral de esmeralda",
    qore: "Mineral de cuarzo",
    adore: "Escombros antiguos",
    ctOre: "Mineral personalizado",
    oabck: "Solo se permite un bloque que contenga las palabras clave\n§8\"_ore, _ore_ ore_\" en su identificador.",
    fptse: "Seguir jugador §c= §aVerdadero\n\n§7Efecto de sonido",
    contr: "Contenedor",
    ttcsa: "La tarea fue cancelada, envíe para guardar de nuevo",
    cgrdi: "Radio de carga",
    coord: "Coordenadas",
    cdSel: "Selección cancelada",
    iwdas: "Interactúa con el dron para §aaceptar§7 la selección.\nRompe un bloque para §ccancelar §7la selección.",
    selMd: "Modo de selección",
    bbtsl: "Rompe un bloque para seleccionar.",
    anten: "Antena",
    owner: "Dueño",
    sktrp: "Agáchate para reparar",
    doutl: "Contorno del Dron",
    outCo: "Color del contorno",
    cusCo: "Color personalizado",
    black: "Negro",
    white: "Blanco",
    red: "Rojo",
    blue: "Azul",
    yellw: "Amarillo",
    green: "Verde",
    purpl: "Púrpura",
    orang: "Naranja",
    pink: "Rosa",
    dBlue: "Azul del dron",
    xpOrb: "Orbe de XP",
    tDete: "Objetivo detectado",
    occlr: "Color Personalizado del Contorno",
    remlq: "Eliminar líquidos",
    tlwsk: "Teletransportarse cuando esté atascado",
}

const portuguese = {
    langu: "Idioma",
    apd: "Drone Multifuncional",
    batt: "Bateria",
    cTask: "Tarefa atual",
    tasks: "Tarefas",
    sTask: "Tarefas simples",
    lock: "Bloqueado",
    fMode: "Modo voo",
    wMode: "Modo andar",
    fplay: "Seguir jogador",
    uplay: "Deixar de seguir jogador",
    dCam: "Câmera do drone",
    setts: "Configurações",
    clTask: "Cancelar tarefa",
    desDro: "Destruir drone",
    yes: "Sim",
    noo: "Não",
    dTasks: "Tarefas do drone",
    mALoc: "Minerar uma localização",
    kllMb: "Matar mobs",
    fALoc: "Preencher uma localização",
    bMobs: "Trazer mobs",
    mNBlo: "Minerar blocos próximos",
    cItem: "Coletar itens",
    oDect: "Detector de minério",
    mItem: "Mover itens",
    cStru: "Clonar estrutura",
    fishg: "Pesca",
    iArea: "Iluminar área",
    rCatt: "Criar gado",
    sShee: "Tosar ovelhas",
    mCows: "Ordenhar vacas",
    lwarn: "Aviso de lag",
    cBatt: "Carregar bateria",
    gTLoc: "Ir para localização",
    sInve: "Salvar inventário",
    tIFCo: "Pegar itens do contêiner",
    dInve: "Soltar inventário",
    sTCam: "Agachar para fechar a câmera",
    aStts: "Configurações da antena",
    wOwnr: "Sem dono",
    swarm: "Enxame",
    sStts: "Configurações do enxame",
    crLoc: "Local de descanso e carga",
    gRLoc: "Ir ao local de descanso ao terminar uma tarefa",
    dName: "Nome do drone",
    lcSSt: "Bloqueado para configurações do enxame",
    sONam: "Mostrar nome do dono",
    vName: "Visível no nome",
    sBPer: "Mostrar porcentagem de bateria",
    nVisy: "Visibilidade do nome:\n§8Para alternar entre sempre mostrar e mostrar parcialmente, é necessário reiniciar o mundo para ter efeito",
    ttPla: "Teletransportar para o jogador\n§8Seguindo o jogador",
    mDttP: "Distância mínima para teletransportar-se para o jogador",
    dOwne: "Dono do drone",
    aFMod: "Modo de voo automático\n§8O drone ativará o modo voo se a bateria estiver acima de 10 por cento",
    tSttF: "Estas são as configurações do",
    dIfSt: "drone, se você salvar, todos os drones deste enxame terão as mesmas configurações",
    dca: "Antena de Controle do Drones",
    drc: "Controle remoto do drone",
    cDSwa: "Criar enxame de drones",
    drones: "Drones",
    dofr: "Drones fora de alcance",
    ndws: "Drones próximos sem enxame",
    addd: "Adicionar drone",
    tandn: "Não há drones próximos",
    add: "Adicionar",
    ndbts: "Drones próximos pertencentes a este enxame",
    remov: "Remover",
    tndts: "Não há drones neste enxame",
    dOpts: "Opções do drone",
    seleD: "Selecionar drone",
    deleD: "Excluir drone",
    deleS: "Excluir enxame",
    dSwrm: "Enxame de drones",
    swarms: "Enxames",
    clTsks: "Cancelar tarefas",
    sName: "Nome do enxame",
    selfD: "Autodestruição",
    none: "§cNenhuma",
    difT: "§cTarefas diferentes",
    dnotS: "Não mostrar",
    parSh: "Mostrar parcialmente",
    alwSh: "Mostrar sempre",
    asr: "Rádio de Varredura da Antena",
    eses: "Emitir sinal a cada - segundos",
    antNa: "Nome da antena",
    nVisi: "Visibilidade do nome",
    aOwnr: "Dono da antena",
    ldTks: "Bloqueado durante uma tarefa",
    onlin: "Online",
    to: "Para",
    from: "De",
    optio: "Opcional",
    llorm: "Lag em áreas grandes ou médias",
    scitw: "Selecionar coordenadas no mundo\n§8Todas as configurações serão ignoradas.",
    bfeqb: "Filtro de blocos\n §8Vazio equivale a todos os blocos",
    dtcrh: "Modo colheita\n§8O drone pegará as plantações prontas para serem colhidas.",
    sabsa: "Recarregar tarefa\n§8Ele procurará novamente por novos blocos na área selecionada",
    ciwbs: "Salvar itens em\n§8Baú onde os itens serão armazenados",
    noSel: "§Nnão selecionado",
    mobfl: "Filtro de Mobs",
    animl: "Animais",
    monst: "Monstros",
    wtcre: "Criaturas aquáticas",
    fMobs: "Criaturas voadoras",
    acree: "Evitar creepers",
    itmob: "Ignorar mobs domesticados",
    src: "Centro do raio de busca",
    srBks: "Raio de busca §8-§r Blocos",
    fptss: "Seguir jogador\n§8As duas configurações anteriores serão ignoradas e estas serão aplicadas\nCentro do raio de busca: localização do jogador\nRaio de Busca - Blocos: 8",
    bccds: "Contêiner de blocos\n§8Baú onde o drone procurará blocos para se abastecer.",
    cmdps: "Modo cultivo\n§8O drone colocará sementes e outros itens nas terras agrícolas.",
    rtsas: "Recarregar tarefa\n§8Procurará novamente espaço para colocar blocos na área selecionada.",
    lmbin: "Deixar mobs em",
    msr: "Raio mínimo de busca",
    mxsr: "Raio máximo de busca",
    mfwbi: "O filtro de mobs será ignorado",
    hsrcl: "Um raio de escaneamento alto pode causar lag.\n§7Raio de escaneamento horizontal §8- §7Blocos:",
    vsrbs: "Raio de escaneamento vertical - Blocos:",
    fpifv: "Seguir jogador\n§8Se você deixar essa opção desativada, o drone vagará pelo mundo procurando blocos válidos para minerar.",
    rdbls: "Raio - Blocos",
    fppsr: "Seguir jogador\n§8As configurações anteriores serão ignoradas e estas serão aplicadas\nCentro do raio de busca: localização do drone\nRaio - Blocos: 8",
    ifeei: "Filtro de itens\n §8Vazio equivale a todos os itens",
    vOre: "Minério Vanilla",
    iore: "Minério de ferro",
    gore: "Minério de ouro",
    dore: "Minério de diamante",
    llore: "Minério de lápis-lazúli",
    rore: "Minério de redstone",
    core: "Minério de carvão",
    cpore: "Minério de cobre",
    eore: "Minério de esmeralda",
    qore: "Minério de quartzo",
    adore: "Detritos ancestrais",
    ctOre: "Minério personalizado",
    oabck: "Somente um bloco contendo as palavras-chave\n§8\"_ore, _ore_ ore_\" em seu identificador é permitido.",
    fptse: "Seguir jogador §c= §aVerdadeiro\n\n§7Efeito sonoro",
    contr: "Contêiner",
    ttcsa: "A tarefa foi cancelada, envie para salvar novamente",
    cgrdi: "Raio de carregamento",
    coord: "Coordenadas",
    cdSel: "Seleção cancelada",
    iwdas: "Interaja com o drone para §aaceitar§7 a seleção.\nQuebre um bloco para §ccancelar §7a seleção.",
    selMd: "Modo de seleção",
    bbtsl: "Quebre um bloco para selecionar.",
    anten: "Antena",
    owner: "Dono",
    sktrp: "Abaixe-se para reparar",
    doutl: "Contorno do Drone",
    outCo: "Cor do contorno",
    cusCo: "Cor personalizada",
    black: "Preto",
    white: "Branco",
    red: "Vermelho",
    blue: "Azul",
    yellw: "Amarelo",
    green: "Verde",
    purpl: "Roxo",
    orang: "Laranja",
    pink: "Rosa",
    dBlue: "Azul do drone",
    xpOrb: "Orbe de XP",
    tDete: "Alvo detectado",
    occlr: "Cor Personalizada do Contorno",
    remlq: "Remover líquidos",
    tlwsk: "Teletransportar quando preso",
}

const chinese = {
	langu: "语言",
	apd: "多功能无人机",
	batt: "电池",
	cTask: "当前任务",
	tasks: "任务",
	sTask: "简单任务",
	lock: "已锁定",
	fMode: "飞行模式",
	wMode: "行走模式",
	fplay: "跟随玩家",
	uplay: "取消跟随玩家",
	dCam: "无人机摄像头",
	setts: "设置",
	clTask: "取消任务",
	desDro: "销毁无人机",
	yes: "是",
	noo: "否  ",
	dTasks: "无人机任务",
	mALoc: "挖掘区域",
	kllMb: "消灭生物",
	fALoc: "填充区域",
	bMobs: "运输生物",
	mNBlo: "挖掘方块",
	cItem: "收集物品",
	oDect: "矿石探测",
	mItem: "搬运物品",
	cStru: "复刻建筑",
	fishg: "钓鱼",
	iArea: "照亮区域",
	rCatt: "饲养牲畜",
	sShee: "剪羊毛",
	mCows: "挤牛奶",
	lwarn: "弱信号警告",
	cBatt: "充电",
	gTLoc: "前往该位置",
	sInve: "保存至无人机存储",
	tIFCo: "从容器中取出物品",
	dInve: "卸除无人机存储",
	sTCam: "轻按“潜行”以关闭摄像头",
	aStts: "天线设置",
	wOwnr: "无所有者",
	swarm: "集群",
	sStts: "集群设置",
	crLoc: "充电和休息地点",
	gRLoc: "完成任务后前往休息地点",
	dName: "无人机名称",
	lcSSt: "锁定集群设置",
	sONam: "显示所有者名称",
	vName: "在实体名称中可见",
	sBPer: "显示电池电量百分比",
	nVisy: "名称可见性\n§8更改此设置后，您必须重新进入世界才能使更改生效！§r",
	ttPla: "传送到玩家\n§8无人机功能“跟随玩家”的附加选项，当距离无人机过远时将传送到无人机所有者身边§r",
	mDttP: "无人机在以下距离时不传送到玩家\n§8传送到玩家的最小距离",
	dOwne: "无人机所有者",
	aFMod: "自动激活飞行模式\n§8当无人机的电池电量大于10%时，无人机将激活飞行模式§r",
	tSttF: "以下是应用此设置的无人机：",
	dIfSt: "注意：更改设置将应用到此集群中所有的无人机！",
	dca: "无人机天线",
	drc: "无人机遥控器",
	cDSwa: "创建无人机集群",
	drones: "无人机",
	dofr: "无人机超出通讯范围",
	ndws: "附近无集群的无人机",
	addd: "添加无人机",
	tandn: "附近没有可用的无人机",
	add: "添加",
	ndbts: "附近属于此集群的无人机",
	remov: "移除",
	tndts: "此集群中没有无人机",
	dOpts: "设置无人机",
	seleD: "选择无人机",
	deleD: "删除无人机",
	deleS: "删除此集群",
	dSwrm: "无人机集群",
	swarms: "集群",
	clTsks: "取消任务",
	sName: "集群名称",
	selfD: "销毁",
	none: "§c无§r",
	difT: "§c不同的任务§r",
	dnotS: "不显示",
	parSh: "部分显示",
	alwSh: "始终显示",
	asr: "天线扫描无线电",
	eses: "每秒发射信号/次",
	antNa: "天线名称",
	nVisi: "名称可见性",
	aOwnr: "天线所有者",
	ldTks: "执行任务期间锁定",
	onlin: "在线",
	to: "到",
	from: "从",
	optio: "可选",
	llorm: "警告：选区过大可能会导致游戏卡顿！",
	scitw: "在世界中选择-交互式选择（推荐）",
	bfeqb: "方块过滤器\n§8留空则表示所有方块§r",
	dtcrh: "收获作物\n§8无人机将收获附近已成熟的作物§r",
	sabsa: "重新加载任务\n§8无人机将重新扫描所选区域中的方块§r",
	ciwbs: "将物品保存在\n§8选择一个用于存储物品的箱子§r",
	noSel: "§N未选择",
	mobfl: "生物过滤器",
	animl: "动物",
	monst: "怪物",
	wtcre: "水下生物",
	fMobs: "飞行生物",
	acree: "避开苦力怕",
	itmob: "忽略被驯服的生物",
	src: "搜索半径中心",
	srBks: "搜索半径",
	fptss: "跟随玩家\n§8这将忽略先前的设置，并应用以下设置:\n搜索半径中心: 玩家位置\n搜索半径: 8 格方块§r",
	bccds: "方块容器\n§8无人机将在此箱子中寻找方块以补充自身§r",
	cmdps: "耕作模式\n§8无人机将在农田上放置作物种子§r",
	rtsas: "重新加载任务\n§8无人机将重新扫描选区内可放置方块的位置§r",
	lmbin: "将生物运输至",
	msr: "最小搜索半径",
	mxsr: "最大搜索半径",
	mfwbi: "忽略生物过滤器",
	hsrcl: "过大的扫描半径可能导致卡顿\n§8水平扫描半径:",
	vsrbs: "垂直搜索半径:",
	fpifv: "跟随玩家\n§8如果禁用此选项，无人机将在世界中漫游，并寻找可挖掘的方块§r",
	rdbls: "半径§8单位: 方块§r",
	fppsr: "跟随玩家\n§8这将忽略先前的设置，并应用以下设置:\n搜索半径中心: 无人机位置\n半径: 8 格方块§r",
	ifeei: "物品过滤器\n §8留空则表示所有物品§r",
	vOre: "Minecraft原版矿石",
	iore: "铁矿石",
	gore: "金矿石",
	dore: "钻石矿石",
	llore: "青金石矿石",
	rore: "红石矿石",
	core: "煤矿石",
	cpore: "铜矿石",
	eore: "绿宝石矿石",
	qore: "下界石英矿石",
	adore: "远古残骸",
	ctOre: "自定义矿石",
	oabck: '§8仅支持在物品标识符包含以下关键字的方块: "_ore, _ore_, ore_"§r',
	fptse: "跟随玩家: §a是§r",
	contr: "容器",
	ttcsa: "任务已取消，请重新提交以保存设置",
	cgrdi: "充电半径",
	coord: "坐标",
	cdSel: "已取消选择",
	iwdas: "右键无人机以§a继续§7\n破坏方块以§c取消§r",
	selMd: "请选择两个基点",
	bbtsl: "破坏方块以进行选择",
	anten: "天线",
	owner: "所有者",
	sktrp: "轻触“潜行”以修复无人机",
    doutl: "无人机轮廓",
    outCo: "轮廓颜色",
    cusCo: "自定义颜色",
    black: "黑色",
    white: "白色",
    red: "红色",
    blue: "蓝色",
    yellw: "黄色",
    green: "绿色",
    purpl: "紫色",
    orang: "橙色",
    pink: "粉色",
    dBlue: "无人机蓝色",
    xpOrb: "经验球",
    tDete: "目标已检测",
    occlr: "自定义轮廓颜色",
    remlq: "移除液体",
    tlwsk: "卡住时传送",
	empty: "空"
}