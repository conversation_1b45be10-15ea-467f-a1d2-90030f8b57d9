"""
ملف اختبار أداة استخراج بيانات مودات mcpedl.com
"""
import sys
import logging
from utils import setup_logging, is_valid_mcpedl_url
from scraper import MCPEDLScraper

def test_url_validation():
    """اختبار التحقق من صحة الروابط"""
    print("🔍 اختبار التحقق من صحة الروابط...")
    
    valid_urls = [
        "https://mcpedl.com/dragon-mounts-v1-3-25/",
        "http://mcpedl.com/some-mod/",
        "https://www.mcpedl.com/another-mod/"
    ]
    
    invalid_urls = [
        "https://google.com",
        "https://minecraft.net",
        "not-a-url",
        ""
    ]
    
    # اختبار الروابط الصحيحة
    for url in valid_urls:
        if is_valid_mcpedl_url(url):
            print(f"✅ رابط صحيح: {url}")
        else:
            print(f"❌ رابط خاطئ (يجب أن يكون صحيح): {url}")
    
    # اختبار الروابط الخاطئة
    for url in invalid_urls:
        if not is_valid_mcpedl_url(url):
            print(f"✅ رابط خاطئ (كما متوقع): {url}")
        else:
            print(f"❌ رابط صحيح (يجب أن يكون خاطئ): {url}")

def test_scraper_basic():
    """اختبار أساسي لكلاس المستخرج"""
    print("\n🔧 اختبار كلاس المستخرج...")
    
    try:
        scraper = MCPEDLScraper()
        print("✅ تم إنشاء كلاس المستخرج بنجاح")
        
        # اختبار جلب صفحة (بدون رابط حقيقي)
        print("🌐 اختبار جلب صفحة...")
        
        # يمكن إضافة رابط حقيقي هنا للاختبار
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        
        print(f"محاولة جلب: {test_url}")
        soup = scraper.fetch_page(test_url)
        
        if soup:
            print("✅ تم جلب الصفحة بنجاح")
            
            # اختبار استخراج العنوان
            title = scraper.extract_title(soup)
            print(f"📝 العنوان المستخرج: {title}")
            
            # اختبار استخراج الفئة
            category = scraper.extract_category(soup)
            print(f"📂 الفئة المستخرجة: {category}")
            
            # اختبار استخراج الإصدارات
            versions = scraper.extract_versions(soup)
            print(f"🎮 الإصدارات المستخرجة: {versions}")
            
        else:
            print("❌ فشل في جلب الصفحة")
        
        scraper.close()
        print("✅ تم إغلاق المستخرج بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المستخرج: {e}")

def test_data_extraction():
    """اختبار استخراج البيانات الكاملة"""
    print("\n📊 اختبار استخراج البيانات الكاملة...")
    
    try:
        scraper = MCPEDLScraper()
        
        # يمكن تغيير هذا الرابط لاختبار مود مختلف
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        
        print(f"استخراج بيانات من: {test_url}")
        mod_data = scraper.scrape_mod_data(test_url)
        
        if mod_data:
            print("✅ تم استخراج البيانات بنجاح!")
            print("\n📋 البيانات المستخرجة:")
            
            for key, value in mod_data.items():
                if isinstance(value, list):
                    print(f"  {key}: {len(value)} عنصر")
                elif isinstance(value, str) and len(value) > 100:
                    print(f"  {key}: {value[:100]}...")
                else:
                    print(f"  {key}: {value}")
        else:
            print("❌ فشل في استخراج البيانات")
        
        scraper.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار استخراج البيانات: {e}")

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار أداة استخراج بيانات مودات mcpedl.com")
    print("=" * 60)
    
    # إعداد نظام التسجيل
    setup_logging(logging.INFO)
    
    try:
        # تشغيل الاختبارات
        test_url_validation()
        test_scraper_basic()
        test_data_extraction()
        
        print("\n" + "=" * 60)
        print("✅ انتهت جميع الاختبارات")
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
