"""
معالج الأخطاء المتقدم للتطبيق
"""
import logging
import traceback
from typing import Optional, Dict, Any
from functools import wraps

class ErrorHandler:
    """كلاس معالجة الأخطاء"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.error_counts = {}
    
    def log_error(self, error: Exception, context: str = "", extra_data: Optional[Dict] = None):
        """تسجيل خطأ مع معلومات إضافية"""
        error_type = type(error).__name__
        
        # تحديث عداد الأخطاء
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # تجهيز رسالة الخطأ
        error_msg = f"خطأ في {context}: {error}"
        
        if extra_data:
            error_msg += f" | بيانات إضافية: {extra_data}"
        
        # تسجيل الخطأ
        self.logger.error(error_msg)
        self.logger.debug(f"تفاصيل الخطأ:\n{traceback.format_exc()}")
    
    def get_error_summary(self) -> Dict[str, int]:
        """الحصول على ملخص الأخطاء"""
        return self.error_counts.copy()
    
    def reset_error_counts(self):
        """إعادة تعيين عدادات الأخطاء"""
        self.error_counts.clear()

# إنشاء معالج أخطاء عام
error_handler = ErrorHandler()

def handle_errors(context: str = "", reraise: bool = False):
    """ديكوريتر لمعالجة الأخطاء"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_handler.log_error(
                    e, 
                    context or func.__name__,
                    {"args": str(args)[:100], "kwargs": str(kwargs)[:100]}
                )
                if reraise:
                    raise
                return None
        return wrapper
    return decorator

class RetryHandler:
    """معالج إعادة المحاولة"""
    
    @staticmethod
    def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
        """ديكوريتر لإعادة المحاولة عند الفشل"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                import time
                
                last_exception = None
                current_delay = delay
                
                for attempt in range(max_retries + 1):
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        last_exception = e
                        
                        if attempt < max_retries:
                            logging.getLogger(__name__).warning(
                                f"المحاولة {attempt + 1} فشلت في {func.__name__}: {e}. "
                                f"إعادة المحاولة خلال {current_delay} ثانية..."
                            )
                            time.sleep(current_delay)
                            current_delay *= backoff
                        else:
                            logging.getLogger(__name__).error(
                                f"فشلت جميع المحاولات ({max_retries + 1}) في {func.__name__}: {e}"
                            )
                
                raise last_exception
            return wrapper
        return decorator

class ValidationError(Exception):
    """خطأ في التحقق من صحة البيانات"""
    pass

class ScrapingError(Exception):
    """خطأ في عملية الاستخراج"""
    pass

class DatabaseError(Exception):
    """خطأ في قاعدة البيانات"""
    pass

class ConfigurationError(Exception):
    """خطأ في الإعدادات"""
    pass
