# -*- coding: utf-8 -*-
"""
اختبار استخراج الصور المتعددة
"""

import sys
import os

# إضافة المجلد الحالي إلى المسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_multiple_images_extraction():
    """اختبار استخراج صور متعددة"""
    print("🖼️ اختبار استخراج الصور المتعددة...")
    print("=" * 50)
    
    try:
        from mcpedl_scraper_module import scrape_mcpedl_mod
        
        # اختبار مع مودات مختلفة
        test_urls = [
            "https://mcpedl.com/ezrtx/",
            "https://mcpedl.com/dragon-mounts-v1-3-25/",
            "https://mcpedl.com/furniture-addon/",
        ]
        
        for i, test_url in enumerate(test_urls, 1):
            print(f"\n🔗 اختبار {i}: {test_url}")
            
            result = scrape_mcpedl_mod(test_url)
            
            if result:
                images = result.get('image_urls', [])
                print(f"✅ تم استخراج {len(images)} صورة")
                
                # تحليل أنواع الصور
                forgecdn_images = []
                mcpedl_images = []
                user_images = []
                other_images = []
                
                for img_url in images:
                    if 'media.forgecdn.net' in img_url:
                        forgecdn_images.append(img_url)
                    elif 'mcpedl.com' in img_url and 'users' not in img_url:
                        mcpedl_images.append(img_url)
                    elif 'users' in img_url or 'gravatar' in img_url:
                        user_images.append(img_url)
                    else:
                        other_images.append(img_url)
                
                print(f"📊 تحليل الصور:")
                print(f"   - صور forgecdn: {len(forgecdn_images)}")
                print(f"   - صور mcpedl: {len(mcpedl_images)}")
                print(f"   - صور مستخدمين: {len(user_images)}")
                print(f"   - صور أخرى: {len(other_images)}")
                
                # عرض الصور
                print(f"📋 قائمة الصور:")
                for j, img_url in enumerate(images, 1):
                    img_type = "❓"
                    if 'media.forgecdn.net' in img_url:
                        img_type = "🎯"  # صورة مود رئيسية
                    elif 'mcpedl.com' in img_url and 'users' not in img_url:
                        img_type = "📸"  # صورة موقع
                    elif 'users' in img_url or 'gravatar' in img_url:
                        img_type = "👤"  # صورة مستخدم
                    
                    print(f"   {j}. {img_type} {img_url}")
                
                # تقييم النتيجة
                if len(images) >= 3:
                    print(f"✅ نجح في استخراج صور متعددة!")
                elif len(images) >= 1:
                    print(f"⚠️ استخرج صور قليلة")
                else:
                    print(f"❌ لم يستخرج أي صور")
                    
            else:
                print(f"❌ فشل في استخراج البيانات")
            
            print("-" * 30)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def test_specific_mod_with_many_images():
    """اختبار مود محدد يحتوي على صور كثيرة"""
    print("\n🎯 اختبار مود بصور متعددة...")
    print("=" * 50)
    
    try:
        from mcpedl_scraper_module import scrape_mcpedl_mod
        
        # مود معروف بوجود صور متعددة
        test_url = "https://mcpedl.com/furniture-addon/"
        print(f"🔗 اختبار: {test_url}")
        
        result = scrape_mcpedl_mod(test_url)
        
        if result:
            images = result.get('image_urls', [])
            print(f"✅ تم استخراج {len(images)} صورة")
            
            # البحث عن صور forgecdn المتوقعة
            forgecdn_count = sum(1 for img in images if 'media.forgecdn.net' in img)
            
            print(f"📊 صور forgecdn: {forgecdn_count}")
            
            if forgecdn_count >= 3:
                print("🎉 ممتاز! تم استخراج صور متعددة من forgecdn")
                return True
            elif forgecdn_count >= 1:
                print("⚠️ تم استخراج بعض الصور من forgecdn")
                return True
            else:
                print("❌ لم يتم استخراج صور من forgecdn")
                return False
        else:
            print("❌ فشل في استخراج البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_image_filtering_effectiveness():
    """اختبار فعالية فلترة الصور"""
    print("\n🔍 اختبار فعالية فلترة الصور...")
    print("=" * 50)
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        
        extractor = MCPEDLExtractorFixed()
        
        # اختبار عينات من الروابط
        test_images = [
            # صور مود صحيحة
            ("https://media.forgecdn.net/attachments/1113/871/ezrtx-5-jpg.jpg", True),
            ("https://media.forgecdn.net/attachments/1113/870/ezrtx-4-jpg.jpg", True),
            ("https://mcpedl.com/wp-content/uploads/2023/mod-image.png", True),
            ("https://r2.mcpedl.com/content/mod-screenshot.png", True),
            
            # صور مستخدمين يجب رفضها
            ("https://secure.gravatar.com/avatar/4d709253272132cf114cba539b5fa0b0", False),
            ("https://r2.mcpedl.com/users/3445213/avatar.png", False),
            ("https://example.com/profile_pic.jpg", False),
            
            # صور مستخدمين قد تكون صور مود
            ("https://r2.mcpedl.com/users/3445213/mod-showcase.png", True),
            ("https://r2.mcpedl.com/users/695192/screenshot.jpg", True),
        ]
        
        correct_decisions = 0
        total_tests = len(test_images)
        
        for img_url, expected_result in test_images:
            actual_result = extractor.is_valid_mod_image(img_url)
            
            if actual_result == expected_result:
                status = "✅"
                correct_decisions += 1
            else:
                status = "❌"
            
            print(f"{status} {img_url[:60]}... -> {actual_result} (متوقع: {expected_result})")
        
        accuracy = (correct_decisions / total_tests) * 100
        print(f"\n📊 دقة الفلترة: {accuracy:.1f}% ({correct_decisions}/{total_tests})")
        
        if accuracy >= 80:
            print("✅ فلترة ممتازة!")
            return True
        elif accuracy >= 60:
            print("⚠️ فلترة جيدة")
            return True
        else:
            print("❌ فلترة تحتاج تحسين")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الفلترة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل لاستخراج الصور المتعددة")
    print("=" * 60)
    
    results = {}
    
    # اختبار استخراج الصور المتعددة
    results['multiple_images'] = test_multiple_images_extraction()
    
    # اختبار مود محدد
    results['specific_mod'] = test_specific_mod_with_many_images()
    
    # اختبار فعالية الفلترة
    results['filtering'] = test_image_filtering_effectiveness()
    
    # تلخيص النتائج
    print("\n📊 ملخص النتائج:")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name}: {status}")
    
    success_rate = sum(results.values()) / len(results)
    print(f"\nمعدل النجاح: {success_rate*100:.1f}%")
    
    if success_rate >= 0.8:
        print("\n🎉 استخراج الصور المتعددة يعمل بشكل ممتاز!")
        print("✅ فلترة ذكية")
        print("✅ استخراج صور متعددة")
        print("✅ تجنب صور المستخدمين")
    elif success_rate >= 0.5:
        print("\n👍 استخراج الصور يعمل جزئياً")
        print("⚠️ قد يحتاج بعض التحسينات")
    else:
        print("\n⚠️ استخراج الصور يحتاج مراجعة")
        print("💡 راجع إعدادات الفلترة")

if __name__ == "__main__":
    main()
