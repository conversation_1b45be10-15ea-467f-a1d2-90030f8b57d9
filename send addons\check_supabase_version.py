import supabase
import sys

print(f"مسار بايثون المستخدم: {sys.executable}")
print(f"إصدار مكتبة supabase-py: {supabase.__version__}")

# حاول استيراد الاستثناءات المحتملة
try:
    from supabase.lib.storage.storage_exception import StorageException
    print("تم بنجاح: from supabase.lib.storage.storage_exception import StorageException")
except ImportError as e:
    print(f"فشل: from supabase.lib.storage.storage_exception import StorageException - الخطأ: {e}")

try:
    from supabase.lib.client_options import StorageException as ClientOptStorageException
    print("تم بنجاح: from supabase.lib.client_options import StorageException")
except ImportError as e:
    print(f"فشل: from supabase.lib.client_options import StorageException - الخطأ: {e}")

try:
    from supabase.utils.exceptions import StorageException as UtilsStorageException
    print("تم بنجاح: from supabase.utils.exceptions import StorageException")
except ImportError as e:
    print(f"فشل: from supabase.utils.exceptions import StorageException - الخطأ: {e}")
