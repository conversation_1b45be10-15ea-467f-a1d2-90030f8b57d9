"""
إعدادات التطبيق ومتغيرات البيئة
"""
import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

class Config:
    """فئة إعدادات التطبيق"""
    
    # إعدادات Supabase
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    SUPABASE_SERVICE_KEY = os.getenv('SUPABASE_SERVICE_KEY')
    
    # إعدادات OpenAI (أو أي نموذج لغوي آخر)
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    
    # إعدادات الاستخراج
    REQUEST_TIMEOUT = 15
    REQUEST_DELAY = 2  # ثواني بين الطلبات
    MAX_RETRIES = 3
    
    # ترويسات HTTP
    HEADERS = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    # محددات CSS لاستخراج البيانات من mcpedl.com
    SELECTORS = {
        'title': 'h1.entry-title, h1.post-title, .post-header h1',
        'description': '.entry-content p, .post-content p, .content p',
        'category': '.breadcrumbs a:last-of-type, .category-link',
        'main_image': '.entry-content img:first-of-type, .post-content img:first-of-type',
        'gallery_images': '.gallery img, .wp-block-gallery img, .entry-content img',
        'version': '.supported-versions, .minecraft-version, .version-info',
        'download_link': '.download-link, .btn-download, a[href*="download"]',
        'file_size': '.file-size, .download-size',
        'creator': '.author-info, .post-author, .creator-name',
        'social_links': '.social-links a, .author-social a',
        'downloads_count': '.download-count, .downloads',
        'likes_count': '.likes-count, .rating'
    }
    
    # رسائل الأخطاء
    ERROR_MESSAGES = {
        'invalid_url': 'رابط غير صحيح أو غير مدعوم',
        'page_not_found': 'الصفحة غير موجودة',
        'connection_error': 'خطأ في الاتصال بالموقع',
        'parsing_error': 'خطأ في تحليل محتوى الصفحة',
        'supabase_error': 'خطأ في الاتصال بقاعدة البيانات',
        'llm_error': 'خطأ في توليد الوصف'
    }
    
    @classmethod
    def validate_config(cls):
        """التحقق من صحة الإعدادات المطلوبة"""
        required_vars = ['SUPABASE_URL', 'SUPABASE_SERVICE_KEY']
        missing_vars = []
        
        for var in required_vars:
            if not getattr(cls, var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        
        return True
