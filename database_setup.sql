-- إعد<PERSON> قاعدة بيانات Supabase لمودات Minecraft
-- يجب تشغيل هذا السكربت في محرر SQL في لوحة تحكم Supabase

-- إن<PERSON>اء جدول المودات الرئيسي
CREATE TABLE IF NOT EXISTS mods (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    description_ar TEXT,
    category TEXT,
    image_urls JSONB,
    version TEXT,
    size TEXT,
    download_url TEXT,
    source_url TEXT UNIQUE,
    downloads INTEGER DEFAULT 0,
    likes INTEGER DEFAULT 0,
    creator_name TEXT,
    creator_contact_info TEXT,
    creator_social_channels JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_mods_name ON mods(name);
CREATE INDEX IF NOT EXISTS idx_mods_category ON mods(category);
CREATE INDEX IF NOT EXISTS idx_mods_creator ON mods(creator_name);
CREATE INDEX IF NOT EXISTS idx_mods_source_url ON mods(source_url);
CREATE INDEX IF NOT EXISTS idx_mods_created_at ON mods(created_at);

-- إنشاء فهرس GIN للبحث في JSON
CREATE INDEX IF NOT EXISTS idx_mods_image_urls ON mods USING GIN(image_urls);
CREATE INDEX IF NOT EXISTS idx_mods_social_channels ON mods USING GIN(creator_social_channels);

-- إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء trigger لتحديث updated_at
DROP TRIGGER IF EXISTS update_mods_updated_at ON mods;
CREATE TRIGGER update_mods_updated_at
    BEFORE UPDATE ON mods
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- إنشاء جدول إحصائيات الاستخراج (اختياري)
CREATE TABLE IF NOT EXISTS scraping_stats (
    id BIGSERIAL PRIMARY KEY,
    date DATE DEFAULT CURRENT_DATE,
    total_processed INTEGER DEFAULT 0,
    successful INTEGER DEFAULT 0,
    failed INTEGER DEFAULT 0,
    duration_minutes INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول سجل الأخطاء (اختياري)
CREATE TABLE IF NOT EXISTS error_logs (
    id BIGSERIAL PRIMARY KEY,
    error_type TEXT,
    error_message TEXT,
    source_url TEXT,
    context TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء view للإحصائيات السريعة
CREATE OR REPLACE VIEW mods_summary AS
SELECT 
    COUNT(*) as total_mods,
    COUNT(DISTINCT category) as total_categories,
    COUNT(DISTINCT creator_name) as total_creators,
    SUM(downloads) as total_downloads,
    AVG(downloads) as avg_downloads,
    COUNT(*) FILTER (WHERE image_urls IS NOT NULL) as mods_with_images,
    COUNT(*) FILTER (WHERE description_ar IS NOT NULL) as mods_with_arabic_desc,
    COUNT(*) FILTER (WHERE creator_social_channels IS NOT NULL) as mods_with_social
FROM mods;

-- إنشاء view لأشهر الفئات
CREATE OR REPLACE VIEW popular_categories AS
SELECT 
    category,
    COUNT(*) as mod_count,
    SUM(downloads) as total_downloads,
    AVG(downloads) as avg_downloads
FROM mods 
WHERE category IS NOT NULL
GROUP BY category
ORDER BY mod_count DESC;

-- إنشاء view لأنشط المطورين
CREATE OR REPLACE VIEW active_creators AS
SELECT 
    creator_name,
    COUNT(*) as mod_count,
    SUM(downloads) as total_downloads,
    AVG(downloads) as avg_downloads,
    MAX(created_at) as last_mod_date
FROM mods 
WHERE creator_name IS NOT NULL
GROUP BY creator_name
ORDER BY mod_count DESC;

-- إضافة تعليقات للجدول والأعمدة
COMMENT ON TABLE mods IS 'جدول مودات Minecraft المستخرجة من mcpedl.com';
COMMENT ON COLUMN mods.name IS 'اسم المود';
COMMENT ON COLUMN mods.description IS 'الوصف المولد باللغة الإنجليزية';
COMMENT ON COLUMN mods.description_ar IS 'الوصف المولد باللغة العربية';
COMMENT ON COLUMN mods.category IS 'فئة المود';
COMMENT ON COLUMN mods.image_urls IS 'روابط الصور بتنسيق JSON';
COMMENT ON COLUMN mods.version IS 'إصدارات Minecraft المدعومة';
COMMENT ON COLUMN mods.size IS 'حجم ملف المود';
COMMENT ON COLUMN mods.download_url IS 'رابط تحميل المود';
COMMENT ON COLUMN mods.source_url IS 'رابط الصفحة الأصلية';
COMMENT ON COLUMN mods.downloads IS 'عدد التحميلات';
COMMENT ON COLUMN mods.likes IS 'عدد الإعجابات';
COMMENT ON COLUMN mods.creator_name IS 'اسم مطور المود';
COMMENT ON COLUMN mods.creator_contact_info IS 'معلومات الاتصال بالمطور';
COMMENT ON COLUMN mods.creator_social_channels IS 'قنوات التواصل الاجتماعي بتنسيق JSON';

-- إنشاء سياسات الأمان (Row Level Security) - اختياري
-- ALTER TABLE mods ENABLE ROW LEVEL SECURITY;

-- سياسة للقراءة العامة
-- CREATE POLICY "Allow public read access" ON mods FOR SELECT USING (true);

-- سياسة للكتابة للمستخدمين المصرح لهم فقط
-- CREATE POLICY "Allow authenticated insert" ON mods FOR INSERT WITH CHECK (auth.role() = 'authenticated');
-- CREATE POLICY "Allow authenticated update" ON mods FOR UPDATE USING (auth.role() = 'authenticated');

-- إنشاء دالة للبحث في المودات
CREATE OR REPLACE FUNCTION search_mods(search_term TEXT)
RETURNS TABLE(
    id BIGINT,
    name TEXT,
    description TEXT,
    category TEXT,
    creator_name TEXT,
    downloads INTEGER,
    relevance REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.name,
        m.description,
        m.category,
        m.creator_name,
        m.downloads,
        (
            CASE WHEN m.name ILIKE '%' || search_term || '%' THEN 3 ELSE 0 END +
            CASE WHEN m.description ILIKE '%' || search_term || '%' THEN 2 ELSE 0 END +
            CASE WHEN m.category ILIKE '%' || search_term || '%' THEN 1 ELSE 0 END
        )::REAL as relevance
    FROM mods m
    WHERE 
        m.name ILIKE '%' || search_term || '%' OR
        m.description ILIKE '%' || search_term || '%' OR
        m.category ILIKE '%' || search_term || '%' OR
        m.creator_name ILIKE '%' || search_term || '%'
    ORDER BY relevance DESC, m.downloads DESC;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة لتنظيف البيانات القديمة (اختياري)
CREATE OR REPLACE FUNCTION cleanup_old_error_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM error_logs 
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- جدولة تنظيف السجلات القديمة (يتطلب pg_cron extension)
-- SELECT cron.schedule('cleanup-error-logs', '0 2 * * *', 'SELECT cleanup_old_error_logs();');
