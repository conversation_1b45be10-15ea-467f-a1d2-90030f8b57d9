# -*- coding: utf-8 -*-
"""
إعداد Gemini API لإنشاء أوصاف ذكية
"""

import json
import os
from typing import Optional

def setup_gemini_api():
    """إعداد مفتاح Gemini API"""
    print("🔑 إعداد Gemini API لإنشاء أوصاف ذكية")
    print("=" * 50)
    
    print("📋 للحصول على مفتاح API:")
    print("1. اذهب إلى: https://makersuite.google.com/app/apikey")
    print("2. سجل الدخول بحساب Google")
    print("3. انقر على 'Create API Key'")
    print("4. انسخ المفتاح")
    print()
    
    # طلب المفتاح من المستخدم
    api_key = input("أدخل مفتاح Gemini API (أو اتركه فارغاً للتخطي): ").strip()
    
    if not api_key:
        print("⚠️ تم تخطي إعداد Gemini API")
        print("💡 ستستخدم الأداة الأوصاف الاحتياطية")
        return False
    
    # حفظ المفتاح في ملف config
    config_data = {
        "gemini_api_key": api_key,
        "ai_descriptions_enabled": True,
        "description_language": "both"  # both, english, arabic
    }
    
    try:
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        print("✅ تم حفظ مفتاح API في config.json")
        
        # اختبار المفتاح
        if test_gemini_api(api_key):
            print("🎉 Gemini API يعمل بشكل صحيح!")
            print("✅ ستحصل الآن على أوصاف ذكية بالعربية والإنجليزية")
            return True
        else:
            print("❌ مفتاح API غير صحيح أو هناك مشكلة في الاتصال")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في حفظ المفتاح: {e}")
        return False

def test_gemini_api(api_key: str) -> bool:
    """اختبار مفتاح Gemini API"""
    try:
        import google.generativeai as genai
        
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-pro')
        
        # اختبار بسيط
        response = model.generate_content("Say hello in one word")
        
        if response and response.text:
            print(f"📝 اختبار API: {response.text.strip()}")
            return True
        else:
            return False
            
    except ImportError:
        print("⚠️ مكتبة google-generativeai غير مثبتة")
        print("💡 قم بتثبيتها: pip install google-generativeai")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار API: {e}")
        return False

def check_current_config():
    """فحص الإعدادات الحالية"""
    print("🔍 فحص الإعدادات الحالية...")
    
    config_files = ['config.json', 'settings.json']
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                if 'gemini_api_key' in config:
                    api_key = config['gemini_api_key']
                    if api_key and len(api_key) > 10:
                        print(f"✅ تم العثور على مفتاح API في {config_file}")
                        print(f"🔑 المفتاح: {api_key[:10]}...{api_key[-5:]}")
                        
                        # اختبار المفتاح
                        if test_gemini_api(api_key):
                            print("✅ المفتاح يعمل بشكل صحيح")
                            return True
                        else:
                            print("❌ المفتاح لا يعمل")
                            return False
                    else:
                        print(f"⚠️ مفتاح فارغ في {config_file}")
                else:
                    print(f"⚠️ لا يوجد مفتاح في {config_file}")
                    
            except Exception as e:
                print(f"❌ خطأ في قراءة {config_file}: {e}")
    
    print("⚠️ لم يتم العثور على مفتاح API")
    return False

def install_gemini_library():
    """تثبيت مكتبة Gemini"""
    print("📦 تثبيت مكتبة Google Generative AI...")
    
    try:
        import subprocess
        import sys
        
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'google-generativeai'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تثبيت المكتبة بنجاح")
            return True
        else:
            print(f"❌ خطأ في التثبيت: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تثبيت المكتبة: {e}")
        return False

def create_example_config():
    """إنشاء ملف config مثال"""
    example_config = {
        "gemini_api_key": "YOUR_API_KEY_HERE",
        "ai_descriptions_enabled": True,
        "description_language": "both",
        "max_description_length": 500,
        "fallback_descriptions": True
    }
    
    try:
        with open('config_example.json', 'w', encoding='utf-8') as f:
            json.dump(example_config, f, indent=2, ensure_ascii=False)
        
        print("📄 تم إنشاء ملف config_example.json")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 مساعد إعداد Gemini API")
    print("=" * 50)
    
    # فحص الإعدادات الحالية
    if check_current_config():
        print("\n🎉 Gemini API مُعد بالفعل ويعمل بشكل صحيح!")
        
        choice = input("\nهل تريد إعادة الإعداد؟ (y/n): ").lower()
        if choice != 'y':
            return
    
    # فحص المكتبة
    try:
        import google.generativeai
        print("✅ مكتبة Google Generative AI متوفرة")
    except ImportError:
        print("⚠️ مكتبة Google Generative AI غير متوفرة")
        
        choice = input("هل تريد تثبيتها؟ (y/n): ").lower()
        if choice == 'y':
            if not install_gemini_library():
                print("❌ فشل في تثبيت المكتبة")
                return
        else:
            print("⚠️ لن تعمل الأوصاف الذكية بدون المكتبة")
            return
    
    # إعداد API
    if setup_gemini_api():
        print("\n🎉 تم إعداد Gemini API بنجاح!")
        print("✅ الآن ستحصل على أوصاف ذكية ومفصلة")
        print("🌟 جرب استخراج مود من MCPEDL لترى الفرق")
    else:
        print("\n⚠️ لم يتم إعداد Gemini API")
        print("💡 ستستخدم الأداة الأوصاف الاحتياطية")
    
    # إنشاء ملف مثال
    create_example_config()
    
    print("\n📋 ملاحظات مهمة:")
    print("• احتفظ بمفتاح API في مكان آمن")
    print("• لا تشارك المفتاح مع أحد")
    print("• يمكنك تغيير المفتاح في أي وقت من config.json")
    print("• إذا نفدت حصة API، ستستخدم الأداة الأوصاف الاحتياطية")

if __name__ == "__main__":
    main()
