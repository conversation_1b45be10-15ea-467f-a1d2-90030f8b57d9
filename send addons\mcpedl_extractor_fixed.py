# -*- coding: utf-8 -*-
"""
مستخرج MCPEDL محسن يعمل مع HTML الحقيقي
"""

import re
import json
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin

# محاولة استيراد الفلتر المحسن
try:
    from mcpedl_image_filter_enhanced import MCPEDLImageFilter
    ENHANCED_FILTER_AVAILABLE = True
except ImportError:
    ENHANCED_FILTER_AVAILABLE = False

class MCPEDLExtractorFixed:
    """مستخرج محسن يعمل مع HTML الحقيقي من MCPEDL"""

    def extract_mod_data(self, html_content: str, url: str, generate_ai_descriptions: bool = True) -> Optional[Dict[str, Any]]:
        """استخراج بيانات المود من HTML مع إنشاء أوصاف ذكية"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # استخراج البيانات الأساسية
            basic_description = self.extract_description(soup)

            mod_data = {
                'name': self.extract_title(soup),
                'description': basic_description,
                'description_arabic': '',  # سيتم إنشاؤه بواسطة AI
                'category': self.extract_category(soup),
                'image_urls': self.extract_images(soup, url),
                'version': self.extract_version(soup),
                'size': self.extract_size(soup),
                'download_url': self.extract_download_url(soup, url),
                'creator_name': self.extract_creator_name(soup),
                'creator_contact_info': '',
                'creator_social_channels': [],
                'source_url': url
            }

            print(f"📊 استخراج البيانات الأساسية:")
            print(f"- الاسم: {mod_data['name']}")
            print(f"- الفئة: {mod_data['category']}")
            print(f"- المطور: {mod_data['creator_name']}")
            print(f"- عدد الصور: {len(mod_data['image_urls'])}")
            print(f"- طول الوصف الأساسي: {len(mod_data['description'])} حرف")

            # إنشاء أوصاف ذكية بواسطة AI
            if generate_ai_descriptions:
                try:
                    print("🤖 إنشاء أوصاف ذكية بواسطة AI...")

                    # استخراج المحتوى الكامل للصفحة
                    full_content = self.extract_full_content(soup)

                    # إنشاء الأوصاف باستخدام Gemini
                    english_desc, arabic_desc = self.generate_ai_descriptions(mod_data, full_content)

                    if english_desc and len(english_desc) > 100:
                        mod_data['description'] = english_desc
                        print(f"✅ تم إنشاء وصف إنجليزي: {len(english_desc)} حرف")

                    if arabic_desc and len(arabic_desc) > 100:
                        mod_data['description_arabic'] = arabic_desc
                        print(f"✅ تم إنشاء وصف عربي: {len(arabic_desc)} حرف")

                except Exception as e:
                    print(f"⚠️ خطأ في إنشاء الأوصاف الذكية: {e}")
                    print("📝 سيتم استخدام الوصف الأساسي المستخرج")

            return mod_data

        except Exception as e:
            print(f"❌ خطأ في استخراج البيانات: {e}")
            return None

    def extract_title(self, soup: BeautifulSoup) -> str:
        """استخراج عنوان المود"""
        # محاولة استخراج من h1
        h1_element = soup.find('h1', class_='post-page__title')
        if h1_element:
            title = h1_element.get_text().strip()
            return self.clean_title(title)

        # محاولة استخراج من title tag
        title_element = soup.find('title')
        if title_element:
            title = title_element.get_text().strip()
            # إزالة "| Minecraft PE Addons" وما شابه
            title = title.split('|')[0].strip()
            return self.clean_title(title)

        return ""

    def clean_title(self, title: str) -> str:
        """تنظيف عنوان المود"""
        # إزالة النصوص غير المرغوبة
        unwanted = [
            '| Minecraft PE Addons',
            '| MCPEDL',
            '- MCPEDL',
            'MCPEDL'
        ]

        for unwanted_text in unwanted:
            title = title.replace(unwanted_text, '').strip()

        # اختصار الأسماء الطويلة
        if ': Community Edition' in title:
            title = title.replace(': Community Edition', '')
        elif ' Community Edition' in title:
            title = title.replace(' Community Edition', '')

        return title.strip()

    def extract_description(self, soup: BeautifulSoup) -> str:
        """استخراج وصف مفصل للمود"""
        description_parts = []

        print("🔍 بدء استخراج الوصف...")

        # 1. البحث في المقدمة الرئيسية
        intro_selectors = [
            'div.introduction-field',
            'div.description-field',
            'div.post-page__content p',
            'div.entry p'
        ]

        for selector in intro_selectors:
            elements = soup.select(selector)
            for element in elements[:3]:  # أول 3 عناصر
                text = element.get_text().strip()
                if text and len(text) > 30 and self.is_valid_description_text(text):
                    description_parts.append(text)
                    print(f"✅ وصف من {selector}: {text[:100]}...")

        # 2. البحث في changelog للمميزات
        changelog_div = soup.find('div', class_='changelog-description')
        if changelog_div:
            # البحث عن قوائم المميزات
            lists = changelog_div.find_all(['ul', 'ol'])
            for list_elem in lists:
                items = list_elem.find_all('li')
                if len(items) >= 2:  # قائمة مميزات
                    features = []
                    for item in items[:5]:  # أول 5 مميزات
                        feature_text = item.get_text().strip()
                        if feature_text and len(feature_text) > 5:
                            features.append(f"• {feature_text}")

                    if features:
                        features_text = " ".join(features)
                        description_parts.append(features_text)
                        print(f"✅ مميزات من changelog: {len(features)} مميزة")

            # البحث في فقرات changelog
            paragraphs = changelog_div.find_all('p')
            for p in paragraphs[:2]:
                text = p.get_text().strip()
                if text and len(text) > 30 and self.is_valid_description_text(text):
                    description_parts.append(text)
                    print(f"✅ وصف من changelog: {text[:100]}...")

        # 3. البحث في النص العام
        all_paragraphs = soup.find_all('p')
        for p in all_paragraphs[:10]:  # أول 10 فقرات
            text = p.get_text().strip()
            if (text and len(text) > 50 and
                self.is_valid_description_text(text) and
                text not in [part for part in description_parts]):
                description_parts.append(text)
                print(f"✅ وصف عام: {text[:100]}...")
                if len(description_parts) >= 5:  # حد أقصى
                    break

        # دمج الوصف
        if description_parts:
            # أخذ أفضل الأجزاء
            final_description = self.combine_description_parts(description_parts)
            print(f"📝 الوصف النهائي: {len(final_description)} حرف")
            return final_description

        # وصف افتراضي
        return "A Minecraft addon that adds new features and content to enhance your gameplay experience."

    def is_valid_description_text(self, text: str) -> bool:
        """التحقق من صحة نص الوصف"""
        # تجاهل النصوص غير المفيدة
        unwanted_phrases = [
            'select version for changelog',
            'skip to downloads',
            'you may also like',
            'installation guides',
            'android',
            'ios',
            'windows 10',
            'credits:',
            'feedback:',
            'support',
            'donation'
        ]

        text_lower = text.lower()
        if any(phrase in text_lower for phrase in unwanted_phrases):
            return False

        # يجب أن يحتوي على كلمات مفيدة
        useful_keywords = [
            'addon', 'mod', 'minecraft', 'feature', 'add', 'new',
            'dragon', 'texture', 'shader', 'pack', 'block', 'item'
        ]

        if any(keyword in text_lower for keyword in useful_keywords):
            return True

        # إذا كان النص طويل ومفصل
        return len(text) > 100

    def combine_description_parts(self, parts: List[str]) -> str:
        """دمج أجزاء الوصف بذكاء"""
        if not parts:
            return ""

        # ترتيب الأجزاء حسب الأهمية
        sorted_parts = sorted(parts, key=lambda x: len(x), reverse=True)

        # أخذ أفضل 3 أجزاء
        best_parts = sorted_parts[:3]

        # دمج مع تجنب التكرار
        combined = []
        for part in best_parts:
            # تنظيف النص
            clean_part = part.strip()
            if clean_part and clean_part not in combined:
                combined.append(clean_part)

        # دمج نهائي
        final_text = " ".join(combined)

        # تحديد الطول
        if len(final_text) > 1000:
            final_text = final_text[:1000] + "..."

        return final_text

    def extract_category(self, soup: BeautifulSoup) -> str:
        """استخراج فئة المود"""
        # البحث في breadcrumbs
        breadcrumbs = soup.find('span', class_='content__title category_list')
        if breadcrumbs:
            links = breadcrumbs.find_all('a')
            for link in links:
                text = link.get_text().lower()
                if 'addon' in text:
                    return 'Addons'
                elif 'shader' in text:
                    return 'Shaders'
                elif 'texture' in text or 'pack' in text:
                    return 'Texture Pack'

        # افتراضي
        return 'Addons'

    def extract_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """استخراج جميع روابط الصور المتعلقة بالمود من محتوى المقال"""
        print("🔍 بدء استخراج الصور من محتوى المقال...")

        # استخدام الفلتر المحسن إذا كان متوفراً
        if ENHANCED_FILTER_AVAILABLE:
            print("🚀 استخدام الفلتر المحسن للصور...")
            try:
                filter_obj = MCPEDLImageFilter()
                filtered_images = filter_obj.filter_images_from_html(soup, base_url)
                if filtered_images:
                    print(f"✅ تم استخراج {len(filtered_images)} صورة باستخدام الفلتر المحسن")
                    return filtered_images
                else:
                    print("⚠️ الفلتر المحسن لم يجد صور، التبديل للطريقة التقليدية...")
            except Exception as e:
                print(f"⚠️ خطأ في الفلتر المحسن: {e}")
                print("🔄 التبديل للطريقة التقليدية...")

        # الطريقة التقليدية
        image_urls = []

        # 1. البحث في og:image (الصورة الرئيسية)
        og_image = soup.find('meta', {'data-hid': 'og:image'})
        if not og_image:
            og_image = soup.find('meta', property='og:image')
        if og_image and og_image.get('content'):
            image_url = og_image['content']
            if self.is_valid_mod_image(image_url):
                image_urls.append(image_url)
                print(f"✅ صورة رئيسية (og:image): {image_url}")

        # 2. البحث المكثف في جميع الصور المرفقة الحقيقية أولاً
        print("🔍 البحث المكثف في جميع الصور المرفقة الحقيقية...")
        all_html = str(soup)

        # أنماط دقيقة للصور الحقيقية فقط - تحسين الأنماط
        real_image_patterns = [
            # forgecdn patterns - دقيقة جداً
            r'https://media\.forgecdn\.net/attachments/\d+/\d+/[a-zA-Z0-9\-_\.]+\.(?:jpg|jpeg|png|gif|webp)',
            r'media\.forgecdn\.net/attachments/\d+/\d+/[a-zA-Z0-9\-_\.]+\.(?:jpg|jpeg|png|gif|webp)',

            # MCPEDL patterns - دقيقة ومحسنة
            r'https://api\.mcpedl\.com/storage/[^"\s<>]+\.(?:jpg|jpeg|png|gif|webp)',
            r'https://r2\.mcpedl\.com/content/[^"\s<>/users]+\.(?:jpg|jpeg|png|gif|webp)',
            r'https://mcpedl\.com/wp-content/uploads/[^"\s<>]+\.(?:jpg|jpeg|png|gif|webp)',

            # أنماط إضافية للصور المرفقة
            r'https://[^"\s<>]*attachments[^"\s<>]*\.(?:jpg|jpeg|png|gif|webp)',
            r'https://[^"\s<>]*screenshots[^"\s<>]*\.(?:jpg|jpeg|png|gif|webp)',
        ]

        for pattern in real_image_patterns:
            attachment_urls = re.findall(pattern, all_html, re.IGNORECASE)
            print(f"🔍 تم العثور على {len(attachment_urls)} رابط حقيقي بالنمط: {pattern[:50]}...")

            for url in attachment_urls:
                # تنظيف الرابط من أي أحرف إضافية
                clean_url = url.strip('"\'<>)')

                # إضافة البروتوكول إذا كان مفقوداً
                if clean_url.startswith('media.forgecdn.net'):
                    clean_url = 'https://' + clean_url

                if clean_url not in image_urls and self.is_valid_mod_image(clean_url):
                    image_urls.append(clean_url)
                    print(f"✅ صورة حقيقية: {clean_url}")
                elif clean_url in image_urls:
                    print(f"⚠️ صورة مكررة: {clean_url[:50]}...")

        # 3. البحث في محتوى المقال الرئيسي
        print("🔍 البحث في محتوى المقال الرئيسي...")

        # تحديد منطقة المحتوى الرئيسية - توسيع القائمة
        content_selectors = [
            'article',                          # المقال الرئيسي
            'div.post-page__content',          # محتوى الصفحة
            'div.entry',                       # المحتوى العام
            'div.content',                     # المحتوى
            'div.post-content',                # محتوى المنشور
            'div.article-content',             # محتوى المقال
            'main',                            # المحتوى الرئيسي
            'div.changelog-description',       # وصف التغييرات
            'div.description-field',           # حقل الوصف
            'div.introduction-field',          # حقل المقدمة
        ]

        content_areas = []
        for selector in content_selectors:
            areas = soup.select(selector)
            content_areas.extend(areas)

        if content_areas:
            print(f"✅ تم العثور على {len(content_areas)} منطقة محتوى")

            # البحث في جميع مناطق المحتوى
            for area_idx, content_area in enumerate(content_areas):
                content_images = content_area.find_all('img')
                print(f"🔍 منطقة {area_idx + 1}: {len(content_images)} صورة")

                for i, img in enumerate(content_images, 1):
                    src = self.get_image_src(img)

                    if src and self.is_valid_mod_image(src):
                        if src not in image_urls:
                            image_urls.append(src)
                            print(f"✅ صورة من محتوى المقال: {src}")
                        else:
                            print(f"⚠️ صورة مكررة: {src[:50]}...")
        else:
            print("⚠️ لم يتم العثور على منطقة المحتوى الرئيسية")

        # 4. البحث في قسم الصور المخصص
        print("🔍 البحث في أقسام الصور المخصصة...")
        image_section_selectors = [
            'div[class*="image"]',
            'div[class*="gallery"]',
            'div[class*="screenshot"]',
            'section[class*="image"]',
            'div[id*="image"]',
            'div[id*="gallery"]',
        ]

        for selector in image_section_selectors:
            sections = soup.select(selector)
            for section in sections:
                section_images = section.find_all('img')
                for img in section_images:
                    src = self.get_image_src(img)
                    if src and self.is_valid_mod_image(src) and src not in image_urls:
                        image_urls.append(src)
                        print(f"✅ صورة من قسم الصور: {src}")

        # 5. البحث في روابط الصور المباشرة في النص
        print("🔍 البحث في روابط الصور المباشرة...")
        all_links = soup.find_all('a')
        for link in all_links:
            href = link.get('href', '')
            if href and any(ext in href.lower() for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']):
                if self.is_valid_mod_image(href) and href not in image_urls:
                    image_urls.append(href)
                    print(f"✅ صورة من رابط مباشر: {href}")

        # 6. البحث في عناصر data attributes
        print("🔍 البحث في data attributes...")
        for element in soup.find_all(attrs={'data-src': True}):
            data_src = element.get('data-src')
            if data_src and self.is_valid_mod_image(data_src) and data_src not in image_urls:
                image_urls.append(data_src)
                print(f"✅ صورة من data-src: {data_src}")

        for element in soup.find_all(attrs={'data-lazy-src': True}):
            data_lazy_src = element.get('data-lazy-src')
            if data_lazy_src and self.is_valid_mod_image(data_lazy_src) and data_lazy_src not in image_urls:
                image_urls.append(data_lazy_src)
                print(f"✅ صورة من data-lazy-src: {data_lazy_src}")

        # 7. البحث في جميع script tags للصور المخفية
        print("🔍 البحث في JavaScript للصور المخفية...")
        script_tags = soup.find_all('script')
        for script in script_tags:
            script_content = script.string or ''
            if script_content:
                # البحث عن روابط forgecdn مباشرة في JavaScript
                js_forgecdn_urls = re.findall(r'https://media\.forgecdn\.net/attachments/\d+/\d+/[^"\s\'<>]+', script_content)
                for url in js_forgecdn_urls:
                    clean_url = url.strip('"\'<>')
                    if clean_url not in image_urls and self.is_valid_mod_image(clean_url):
                        image_urls.append(clean_url)
                        print(f"✅ صورة من JavaScript: {clean_url}")

        # 8. البحث في JSON data المخفي
        print("🔍 البحث في JSON data المخفي...")
        json_scripts = soup.find_all('script', type='application/json')
        for json_script in json_scripts:
            try:
                json_data = json.loads(json_script.string or '{}')
                self._extract_images_from_json(json_data, image_urls)
            except (json.JSONDecodeError, AttributeError):
                continue

        # 9. البحث الشامل في جميع صور الصفحة كبديل أخير (مع فلترة قوية)
        if len(image_urls) < 5:  # إذا لم نجد صور كافية
            print("🔍 البحث الشامل في جميع صور الصفحة...")
            all_images = soup.find_all('img')
            for img in all_images:
                src = self.get_image_src(img)
                if src and self.is_valid_mod_image(src) and src not in image_urls:
                    # فحص إضافي لتجنب صور "You may also like"
                    img_parent = img.parent
                    skip_image = False

                    # فحص النص المحيط بالصورة
                    if img_parent:
                        parent_text = img_parent.get_text().lower()
                        unwanted_sections = [
                            'you may also like', 'related posts', 'similar mods',
                            'recommended', 'suggestions', 'comments', 'responses',
                            'installation guides', 'android', 'ios', 'windows 10'
                        ]

                        if any(unwanted in parent_text for unwanted in unwanted_sections):
                            skip_image = True
                            print(f"❌ تجاهل صورة من قسم غير مرغوب: {src[:50]}...")

                    if not skip_image:
                        image_urls.append(src)
                        print(f"✅ صورة إضافية: {src}")

                        # توقف عند الوصول لعدد معقول
                        if len(image_urls) >= 15:
                            break

        # 10. فلترة نهائية لإزالة الصور غير المرغوبة
        print("🔍 فلترة نهائية للصور...")
        filtered_images = []
        for img_url in image_urls:
            # تجنب صور المودات المقترحة والتعليقات
            if self.is_definitely_mod_image(img_url):
                filtered_images.append(img_url)
            else:
                print(f"❌ تم إزالة صورة غير مؤكدة: {img_url[:50]}...")

        print(f"📊 إجمالي الصور المستخرجة: {len(filtered_images)}")
        return filtered_images[:25]  # حد أقصى 25 صورة

    def _extract_images_from_json(self, json_data, image_urls):
        """استخراج الصور من بيانات JSON"""
        if isinstance(json_data, dict):
            for key, value in json_data.items():
                if isinstance(value, str) and 'forgecdn.net' in value:
                    if value not in image_urls and self.is_valid_mod_image(value):
                        image_urls.append(value)
                        print(f"✅ صورة من JSON: {value}")
                elif isinstance(value, (dict, list)):
                    self._extract_images_from_json(value, image_urls)
        elif isinstance(json_data, list):
            for item in json_data:
                self._extract_images_from_json(item, image_urls)

    def _quick_url_check(self, url: str) -> bool:
        """فحص سريع لوجود الرابط (HEAD request)"""
        try:
            import requests
            response = requests.head(url, timeout=3, allow_redirects=True)
            return response.status_code == 200
        except:
            # في حالة الخطأ، نفترض أن الرابط غير موجود
            return False

    def get_image_src(self, img) -> str:
        """استخراج رابط الصورة من عنصر img مع دعم مصادر متعددة"""
        # محاولة الحصول على الرابط من مصادر مختلفة
        src = (img.get('src') or
               img.get('data-src') or
               img.get('data-lazy-src') or
               img.get('data-original') or
               img.get('data-srcset', '').split(',')[0].strip().split(' ')[0] or
               img.get('srcset', '').split(',')[0].strip().split(' ')[0])

        # إذا لم نجد src، ابحث في العنصر الأب
        if not src and img.parent:
            parent = img.parent
            src = (parent.get('data-src') or
                   parent.get('data-background') or
                   parent.get('data-image'))

        # إذا لم نجد src، ابحث في style attribute
        if not src:
            style = img.get('style', '')
            if style:
                bg_match = re.search(r'background-image:\s*url\(["\']?([^"\']+)["\']?\)', style)
                if bg_match:
                    src = bg_match.group(1)

        if src:
            # تنظيف الرابط
            src = src.strip()

            # إضافة البروتوكول إذا كان مفقوداً
            if src.startswith('//'):
                src = 'https:' + src
            elif src.startswith('/'):
                src = 'https://mcpedl.com' + src
            elif not src.startswith('http'):
                # إذا كان رابط نسبي، أضف النطاق
                src = 'https://mcpedl.com/' + src.lstrip('/')

        return src or ''

    def is_valid_image_url(self, url: str) -> bool:
        """التحقق من صحة رابط الصورة"""
        if not url or len(url) < 10:
            return False

        # تجاهل الصور الثابتة
        static_images = [
            'logo', 'icon', 'avatar', 'favicon', 'empty.png',
            'shield.png', '_nuxt', 'header', 'footer'
        ]

        url_lower = url.lower()
        if any(static in url_lower for static in static_images):
            return False

        # قبول جميع روابط forgecdn (حتى بدون امتداد واضح)
        if 'media.forgecdn.net/attachments' in url_lower:
            return True

        # التحقق من امتداد الصورة للمصادر الأخرى
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
        if not any(ext in url_lower for ext in image_extensions):
            return False

        return True

    def is_valid_mod_image(self, url: str) -> bool:
        """التحقق من صحة صورة المود (فلترة ذكية محسنة)"""
        if not self.is_valid_image_url(url):
            return False

        url_lower = url.lower()

        # رفض صور المستخدمين والتعليقات بوضوح
        user_image_patterns = [
            'gravatar.com',           # صور Gravatar
            'secure.gravatar',        # صور Gravatar الآمنة
            '/avatar',                # أفاتار عام
            'profile_pic',            # صورة الملف الشخصي
            'user_avatar',            # أفاتار المستخدم
            'r2.mcpedl.com/users',    # صور المستخدمين
        ]

        # رفض صور المستخدمين الواضحة
        if any(pattern in url_lower for pattern in user_image_patterns):
            print(f"❌ تم رفض صورة مستخدم: {url[:50]}...")
            return False

        # رفض الصور الثابتة للموقع
        static_site_patterns = [
            'shield.png',
            'shield.6982c20.png',
            '_nuxt',
            'logo',
            'favicon',
            'header',
            'footer',
            'sidebar',
            'banner',
            'advertisement',
            'social-icon',
            'share-button',
            'widget',
            'placeholder',
            'loading',
            'spinner',
            'data:image/svg+xml',
            'data:image/png;base64',
        ]

        if any(pattern in url_lower for pattern in static_site_patterns):
            print(f"❌ تم رفض صورة ثابتة: {url[:50]}...")
            return False

        # قبول الصور من المصادر الموثوقة بقوة
        high_priority_patterns = [
            'media.forgecdn.net/attachments',  # صور المودات الرئيسية
            'edge.forgecdn.net/files',         # ملفات forgecdn
        ]

        if any(pattern in url_lower for pattern in high_priority_patterns):
            print(f"✅ تم قبول صورة مود موثوقة: {url[:50]}...")
            return True

        # قبول الصور من المصادر الموثوقة
        trusted_patterns = [
            'media.forgecdn.net',     # جميع صور forgecdn
            'mcpedl.com/wp-content',  # صور المحتوى
            'mcpedl.com/uploads',     # رفوعات الموقع
            'mcpedl.com/content',     # محتوى الموقع
            'attachments',            # المرفقات
            'screenshots',            # لقطات الشاشة
            'gallery',                # معرض الصور
            'images',                 # مجلد الصور
            'imgur.com',              # صور imgur
            'gyazo.com',              # صور gyazo
            'prnt.sc',                # صور prnt.sc
        ]

        # إذا كان الرابط من مصدر موثوق، اقبله
        if any(pattern in url_lower for pattern in trusted_patterns):
            print(f"✅ تم قبول صورة مود: {url[:50]}...")
            return True

        # فحص إضافي للصور من r2.mcpedl.com (بعد استبعاد المستخدمين)
        if 'r2.mcpedl.com' in url_lower and '/users/' not in url_lower:
            print(f"✅ تم قبول صورة من r2.mcpedl: {url[:50]}...")
            return True

        # قبول الصور من mcpedl.com بشكل عام (ما عدا المستخدمين)
        if 'mcpedl.com' in url_lower and '/users/' not in url_lower:
            print(f"✅ تم قبول صورة من mcpedl: {url[:50]}...")
            return True

        # رفض الصور الصغيرة جداً (أيقونات)
        small_size_patterns = [
            '16x16', '32x32', '64x64', '80x80', '100x100', '120x120'
        ]

        if any(size in url_lower for size in small_size_patterns):
            print(f"❌ تم رفض صورة صغيرة: {url[:50]}...")
            return False

        # قبول الصور الأخرى بحذر إذا كانت تبدو كصور محتوى
        content_indicators = [
            'screenshot', 'image', 'photo', 'picture', 'mod', 'addon',
            'texture', 'skin', 'pack', 'resource', 'behavior'
        ]

        if any(indicator in url_lower for indicator in content_indicators):
            print(f"⚠️ تم قبول صورة محتوى محتملة: {url[:50]}...")
            return True

        # رفض الباقي
        print(f"❌ تم رفض صورة غير مؤكدة: {url[:50]}...")
        return False

    def is_definitely_mod_image(self, url: str) -> bool:
        """فحص نهائي للتأكد من أن الصورة متعلقة بالمود وليست من المودات المقترحة"""
        if not url:
            return False

        url_lower = url.lower()

        # قبول الصور من forgecdn بقوة (هذه صور المودات الحقيقية)
        if 'media.forgecdn.net/attachments' in url_lower:
            return True

        # قبول الصور من مصادر موثوقة للمحتوى
        trusted_content_sources = [
            'mcpedl.com/wp-content/uploads',
            'api.mcpedl.com/storage',
            'r2.mcpedl.com/content'
        ]

        if any(source in url_lower for source in trusted_content_sources):
            # تأكد من أنها ليست صورة مستخدم
            if '/users/' not in url_lower:
                return True

        # رفض الصور الصغيرة والأيقونات
        small_indicators = [
            '16x16', '32x32', '64x64', '80x80', '100x100', '120x120',
            'thumb', 'icon', 'avatar', 'favicon'
        ]

        if any(indicator in url_lower for indicator in small_indicators):
            return False

        # رفض الصور من أقسام غير مرغوبة
        unwanted_sections = [
            'shield.png', '_nuxt', 'gravatar', 'social', 'share',
            'advertisement', 'banner', 'widget', 'placeholder'
        ]

        if any(section in url_lower for section in unwanted_sections):
            return False

        # قبول الصور التي تحتوي على مؤشرات المحتوى
        content_indicators = [
            'screenshot', 'image', 'photo', 'mod', 'addon',
            'texture', 'pack', 'resource', 'behavior'
        ]

        if any(indicator in url_lower for indicator in content_indicators):
            return True

        # قبول الصور من mcpedl.com بشكل عام (ما عدا المستخدمين)
        if 'mcpedl.com' in url_lower and '/users/' not in url_lower:
            return True

        return False

    def extract_version(self, soup: BeautifulSoup) -> str:
        """استخراج إصدار Minecraft المدعوم"""
        # البحث في قسم الإصدارات
        versions_div = soup.find('div', class_='versions')
        if versions_div:
            version_links = versions_div.find_all('a')
            if version_links:
                # أخذ أول إصدار
                first_version = version_links[0].get_text().strip()
                return first_version

        return "1.21+"

    def extract_size(self, soup: BeautifulSoup) -> str:
        """استخراج حجم الملف"""
        # البحث في قسم التحميلات
        downloads_div = soup.find('div', id='downloads')
        if downloads_div:
            download_items = downloads_div.find_all('span')
            for item in download_items:
                text = item.get_text()
                # البحث عن حجم الملف
                size_match = re.search(r'\(([^)]+)\)', text)
                if size_match:
                    return size_match.group(1)

        return ""

    def extract_download_url(self, soup: BeautifulSoup, base_url: str) -> str:
        """استخراج رابط التحميل"""
        # هذا يحتاج معالجة خاصة لأن MCPEDL يستخدم JavaScript للتحميل
        # نعيد الرابط الأصلي للصفحة
        return base_url

    def extract_creator_name(self, soup: BeautifulSoup) -> str:
        """استخراج اسم المطور"""
        # البحث في معلومات المطور
        creator_div = soup.find('div', class_='creator-name')
        if creator_div:
            creator_link = creator_div.find('a')
            if creator_link:
                return creator_link.get_text().strip()

        # البحث في التعليقات
        author_elements = soup.find_all(class_='comments--autor')
        if author_elements:
            return author_elements[0].get_text().strip()

        return ""

    def extract_full_content(self, soup: BeautifulSoup) -> str:
        """استخراج المحتوى الكامل للصفحة لاستخدامه مع AI"""
        content_parts = []

        # استخراج النصوص من العناصر المهمة
        important_selectors = [
            'h1', 'h2', 'h3',  # العناوين
            'div.post-page__content p',  # المحتوى الرئيسي
            'div.changelog-description p',  # وصف التغييرات
            'div.entry p',  # المحتوى العام
            'li',  # قوائم المميزات
            'div.description-field',  # حقل الوصف
            'div.introduction-field'  # حقل المقدمة
        ]

        for selector in important_selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text().strip()
                if text and len(text) > 10:
                    content_parts.append(text)

        # دمج المحتوى
        full_content = ' '.join(content_parts)

        # تنظيف المحتوى
        full_content = re.sub(r'\s+', ' ', full_content)  # إزالة المسافات الزائدة
        full_content = full_content[:3000]  # حد أقصى للطول

        return full_content

    def generate_ai_descriptions(self, mod_data: Dict, content: str) -> tuple:
        """إنشاء أوصاف ذكية باستخدام Gemini"""
        try:
            # محاولة استيراد مولد الوصف
            from gemini_description_generator import GeminiDescriptionGenerator

            # الحصول على مفتاح API من إعدادات التطبيق
            api_key = self.get_gemini_api_key()

            if not api_key:
                print("⚠️ مفتاح Gemini API غير متوفر")
                return self.get_fallback_descriptions(mod_data)

            # إنشاء مولد الوصف
            generator = GeminiDescriptionGenerator(api_key)

            # إنشاء الأوصاف
            english_desc, arabic_desc = generator.generate_descriptions(mod_data, content)

            return english_desc, arabic_desc

        except ImportError:
            print("⚠️ مولد الوصف غير متوفر")
            return self.get_fallback_descriptions(mod_data)
        except Exception as e:
            print(f"⚠️ خطأ في إنشاء الأوصاف: {e}")
            return self.get_fallback_descriptions(mod_data)

    def get_gemini_api_key(self) -> Optional[str]:
        """الحصول على مفتاح Gemini API"""
        try:
            import os

            # البحث في متغيرات البيئة
            api_key = os.getenv('GEMINI_API_KEY')
            if api_key:
                return api_key

            # البحث في ملف config
            config_files = ['config.json', 'settings.json', '../config.json']
            for config_file in config_files:
                if os.path.exists(config_file):
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)

                            # البحث في gemini_api_key
                            if 'gemini_api_key' in config and config['gemini_api_key']:
                                return config['gemini_api_key']

                            # البحث في gemini_api_keys (قائمة)
                            if 'gemini_api_keys' in config and config['gemini_api_keys']:
                                keys = config['gemini_api_keys']
                                if isinstance(keys, list) and len(keys) > 0:
                                    return keys[0]  # أول مفتاح في القائمة

                    except Exception as e:
                        print(f"خطأ في قراءة {config_file}: {e}")
                        continue

            return None

        except Exception as e:
            print(f"خطأ في الحصول على مفتاح API: {e}")
            return None

    def get_fallback_descriptions(self, mod_data: Dict) -> tuple:
        """أوصاف احتياطية"""
        name = mod_data.get('name', 'This Minecraft Addon')
        category = mod_data.get('category', 'addon')

        english = f"""Enhance your Minecraft experience with {name}! This amazing {category.lower()} brings exciting new features and content to your world.

Key Features:
• New gameplay mechanics and content
• High-quality textures and models
• Smooth performance and compatibility
• Easy installation and setup

Perfect for players looking to expand their Minecraft adventure with fresh content. Download now and discover what makes this {category.lower()} special!"""

        arabic = f"""عزز تجربة ماين كرافت الخاصة بك مع {name}! هذه {category} الرائعة تجلب مميزات ومحتوى جديد ومثير إلى عالمك.

المميزات الرئيسية:
• آليات لعب ومحتوى جديد
• تكسشرز ونماذج عالية الجودة
• أداء سلس وتوافق ممتاز
• تثبيت وإعداد سهل

مثالية للاعبين الذين يبحثون عن توسيع مغامرة ماين كرافت مع محتوى جديد. حمّل الآن واكتشف ما يجعل هذه {category} مميزة!"""

        return english, arabic

def test_extractor_with_saved_html():
    """اختبار المستخرج مع HTML المحفوظ"""
    print("🧪 اختبار المستخرج مع HTML المحفوظ...")

    try:
        # قراءة HTML المحفوظ
        with open('debug_specific_url.html', 'r', encoding='utf-8') as f:
            html_content = f.read()

        extractor = MCPEDLExtractorFixed()
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"

        result = extractor.extract_mod_data(html_content, test_url)

        if result:
            print("✅ نجح الاستخراج!")
            print(f"النتيجة: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print("❌ فشل الاستخراج")
            return False

    except FileNotFoundError:
        print("❌ ملف HTML غير موجود")
        return False
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    test_extractor_with_saved_html()
