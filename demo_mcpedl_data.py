#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بيانات تجريبية لاختبار التحسينات عندما يفشل الاتصال مع MCPEDL
"""

import sys
import os

# إضافة مجلد send addons إلى المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
addons_dir = os.path.join(current_dir, 'send addons')
sys.path.insert(0, addons_dir)

def get_demo_mcpedl_data():
    """إرجاع بيانات تجريبية لاختبار التحسينات"""
    
    # بيانات تجريبية تحاكي ما يتم استخراجه من MCPEDL
    demo_data = {
        'name': 'Dragon Mounts',  # اسم مختصر (بدلاً من Dragon Mounts: Community Edition)
        'description': 'Dragon Mounts brings the legendary power of dragons to your Minecraft world. This comprehensive addon features multiple rideable dragon types, each with unique abilities and characteristics. Players can tame, breed, and ride these magnificent creatures while exploring dragon lairs filled with rare treasures. The mod includes custom dragon armor, weapons, and magical items that enhance the dragon-riding experience. Perfect for adventurers seeking epic aerial combat and exploration.',
        'description_arabic': 'Dragon Mounts يجلب قوة التنانين الأسطورية إلى عالم ماين كرافت الخاص بك. تتضمن هذه الإضافة الشاملة أنواع متعددة من التنانين القابلة للركوب، كل منها بقدرات وخصائص فريدة. يمكن للاعبين ترويض وتربية وركوب هذه المخلوقات الرائعة أثناء استكشاف أوكار التنانين المليئة بالكنوز النادرة. يتضمن المود دروع التنانين المخصصة والأسلحة والعناصر السحرية التي تعزز تجربة ركوب التنانين.',
        'category': 'Addons',
        'version': '1.3.25',
        'size': '2.5 MB',
        'download_url': 'https://example.com/dragon-mounts.mcaddon',
        'creator_name': 'Tomanex',  # اسم نظيف (بدلاً من "By Tomanex Published on CurseForge March 17, 2025")
        'creator_contact_info': '<EMAIL>',
        'creator_social_channels': [
            'YouTube: https://youtube.com/channel/UC123456789',
            'Discord: https://discord.gg/dragonmounts',
            'Twitter: https://twitter.com/tomanex_dev'
        ],
        'image_urls': [
            'https://mcpedl.com/wp-content/uploads/2024/dragon-screenshot1.jpg',
            'https://mcpedl.com/wp-content/uploads/2024/dragon-screenshot2.jpg',
            'https://mcpedl.com/wp-content/uploads/2024/dragon-screenshot3.jpg',
            'https://mcpedl.com/wp-content/uploads/2024/dragon-screenshot4.jpg',
            'https://mcpedl.com/wp-content/uploads/2024/dragon-screenshot5.jpg'
        ]
    }
    
    return demo_data

def test_demo_data_integration():
    """اختبار دمج البيانات التجريبية مع الأداة الرئيسية"""
    print("🧪 اختبار دمج البيانات التجريبية")
    print("=" * 50)
    
    try:
        # استيراد دالة ملء الحقول من الأداة الرئيسية
        from mod_processor import populate_fields_from_mcpedl_data
        
        # الحصول على البيانات التجريبية
        demo_data = get_demo_mcpedl_data()
        
        print("📋 البيانات التجريبية:")
        print(f"  📝 الاسم: {demo_data['name']}")
        print(f"  📄 الوصف الإنجليزي: {len(demo_data['description'])} حرف")
        print(f"  📄 الوصف العربي: {len(demo_data['description_arabic'])} حرف")
        print(f"  🖼️ الصور: {len(demo_data['image_urls'])} صورة")
        print(f"  👤 المطور: {demo_data['creator_name']}")
        print(f"  🔗 قنوات التواصل: {len(demo_data['creator_social_channels'])} قناة")
        
        print("\n✅ البيانات التجريبية جاهزة للاستخدام")
        print("💡 يمكن استخدام هذه البيانات لاختبار التحسينات")
        
        return demo_data
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return None
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return None

def create_demo_html():
    """إنشاء ملف HTML تجريبي لاختبار الاستخراج"""
    demo_html = """
<!DOCTYPE html>
<html>
<head>
    <title>Dragon Mounts: Community Edition - MCPEDL</title>
</head>
<body>
    <h1 class="entry-title">Dragon Mounts: Community Edition</h1>
    
    <div class="entry-content">
        <p>Dragon Mounts: Community Edition is an extension add-on based on Dragon Mounts 2 which adds extra dragons that don't fit with the base add-on.</p>
        
        <p>Features:</p>
        <ul>
            <li>Phantom Dragon with special abilities</li>
            <li>Cherry Dragon with unique features</li>
            <li>Compatible with Dragon Mounts 2</li>
            <li>Rideable and tameable dragons</li>
            <li>Custom dragon armor and weapons</li>
        </ul>
        
        <p>By Tomanex Published on CurseForge March 17, 2025 (Updated on May 30, 2025)</p>
        
        <img src="https://mcpedl.com/wp-content/uploads/2024/dragon-screenshot1.jpg" alt="Dragon Screenshot 1">
        <img src="https://mcpedl.com/_nuxt/img/shield.6982c20.png" alt="Shield Icon">
        <img src="https://r2.mcpedl.com/users/3205066/avatar.png" alt="User Avatar">
        <img src="https://mcpedl.com/wp-content/uploads/2024/dragon-screenshot2.jpg" alt="Dragon Screenshot 2">
        
        <div class="social-links">
            <a href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0i...">Facebook</a>
            <a href="https://youtube.com/channel/UC123456789">YouTube Channel</a>
            <a href="https://discord.gg/dragonmounts">Discord Server</a>
            <a href="https://mcpedl.com/internal-link">Internal Link</a>
        </div>
    </div>
</body>
</html>
"""
    
    try:
        with open('demo_mcpedl_page.html', 'w', encoding='utf-8') as f:
            f.write(demo_html)
        print("✅ تم إنشاء ملف HTML تجريبي: demo_mcpedl_page.html")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف HTML: {e}")
        return False

def test_extraction_with_demo_html():
    """اختبار الاستخراج باستخدام HTML تجريبي"""
    print("\n🔍 اختبار الاستخراج مع HTML تجريبي")
    print("=" * 50)
    
    try:
        from mcpedl_selenium_scraper import EnhancedMCPEDLExtractor
        from bs4 import BeautifulSoup
        
        # إنشاء ملف HTML تجريبي
        if not create_demo_html():
            return False
        
        # قراءة الملف
        with open('demo_mcpedl_page.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # تحليل HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        extractor = EnhancedMCPEDLExtractor()
        
        # اختبار استخراج الاسم
        name = extractor.extract_title(soup)
        print(f"📝 الاسم المستخرج: '{name}'")
        
        # اختبار استخراج الصور
        images = extractor.extract_images(soup, 'https://mcpedl.com')
        print(f"🖼️ الصور المستخرجة: {len(images)} صورة")
        for i, img in enumerate(images):
            print(f"  [{i+1}]: {img}")
        
        # اختبار استخراج روابط التواصل
        social_links = extractor.extract_real_social_links(soup)
        print(f"🔗 روابط التواصل: {len(social_links)} رابط")
        for link in social_links:
            print(f"  🔗 {link}")
        
        # اختبار استخراج اسم المطور
        creator = extractor.extract_creator_name(soup)
        print(f"👤 المطور: '{creator}'")
        
        print("\n✅ تم اختبار جميع وظائف الاستخراج")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستخراج: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🎭 أداة البيانات التجريبية لـ MCPEDL")
    print("=" * 60)
    print("هذه الأداة تسمح لك باختبار التحسينات عندما يفشل الاتصال مع MCPEDL")
    
    # اختبار البيانات التجريبية
    demo_data = test_demo_data_integration()
    
    if demo_data:
        # اختبار الاستخراج مع HTML تجريبي
        test_extraction_with_demo_html()
        
        print("\n" + "=" * 60)
        print("🎉 جميع الاختبارات نجحت!")
        print("\n💡 كيفية الاستخدام:")
        print("1. يمكنك استخدام get_demo_mcpedl_data() للحصول على بيانات تجريبية")
        print("2. يمكنك اختبار وظائف الاستخراج باستخدام demo_mcpedl_page.html")
        print("3. البيانات التجريبية تُظهر جميع التحسينات المطبقة:")
        print("   - ✅ اسم مختصر (Dragon Mounts)")
        print("   - ✅ وصف إنجليزي وعربي منفصل")
        print("   - ✅ صور نظيفة (بدون صور ثابتة)")
        print("   - ✅ روابط تواصل حقيقية")
        print("   - ✅ اسم مطور نظيف")
    else:
        print("\n❌ فشل في اختبار البيانات التجريبية")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        input("\nاضغط Enter للخروج...")
