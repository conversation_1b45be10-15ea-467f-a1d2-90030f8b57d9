#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import time

# Change to the correct directory
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# Test Gemini import
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
    print("✅ Gemini library imported successfully")
except ImportError as e:
    GEMINI_AVAILABLE = False
    print(f"❌ Gemini import failed: {e}")

# Test config loading
CONFIG_FILE = "config.json"

def load_config():
    default_config = {"gemini_api_keys": []}
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                if "gemini_api_keys" not in config or not isinstance(config["gemini_api_keys"], list):
                    config["gemini_api_keys"] = []
                return config
        else:
            print(f"❌ Config file {CONFIG_FILE} not found")
            return default_config
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return default_config

# Test configuration
print(f"📁 Current directory: {os.getcwd()}")
print(f"📄 Config file exists: {os.path.exists(CONFIG_FILE)}")

config = load_config()
api_keys = config.get("gemini_api_keys", [])
print(f"🔑 API keys found: {len(api_keys)}")

if api_keys and GEMINI_AVAILABLE:
    print(f"🔑 First key preview: {api_keys[0][:20]}...")
    
    # Test Gemini configuration
    try:
        genai.configure(api_key=api_keys[0])
        model = genai.GenerativeModel('gemini-1.5-flash')
        print("✅ Gemini client configured successfully")
        
        # Simulate the automatic Arabic description generation process
        print("\n🤖 Testing automatic Arabic description generation process...")
        
        # Sample data like what would be extracted from Food Expanded
        mod_name = "Food Expanded"
        mod_category = "Addons"
        scraped_text = "Are you bored with minecraft's food? Don't worry this addon add 60+ new foods! With vanilla feels and survival balanced view of the texture is inspired by Java addon which is the delight's mod"
        manual_features = ["1.21 SUPPORT", "MORE THAN 40+ NEW FOOD", "New version on unusual food expanded check it out!"]
        
        # Convert features to string like the real function does
        manual_features_str = "\n".join([f"- {feature}" for feature in manual_features])
        
        print(f"📝 Mod Name: {mod_name}")
        print(f"📝 Category: {mod_category}")
        print(f"📝 Scraped Text: {scraped_text[:100]}...")
        print(f"📝 Features: {manual_features_str}")
        
        # Determine primary context source (like in the real function)
        primary_context_source = ""
        if manual_features_str:
            primary_context_source = "The user has provided the following key features. Base the description primarily on these:\n\"\"\"\n" + manual_features_str + "\n\"\"\"\n"
            if scraped_text:
                primary_context_source += "\nAdditionally, the following full description was extracted and can be used for supplementary details if relevant:\n\"\"\"\n" + scraped_text[:2000] + "\n\"\"\"\n"
        elif scraped_text:
            primary_context_source = "Based on the following full mod description and features extracted from the source:\n\"\"\"\n" + scraped_text[:2500] + "\n\"\"\"\n"
        
        # Create the exact prompt used in the real function
        prompt = f"""
أنت كاتب محتوى ماين كرافت خبير باللغة العربية. مهمتك هي كتابة وصف جذاب وفريد ومفصل (حوالي 100-200 كلمة) لمحتوى ماين كرافت باللغة العربية، مع الحفاظ على نبرة طبيعية وبشرية.

**اسم المحتوى:** {mod_name if mod_name else "غير محدد"}
**نوع المحتوى:** {mod_category}

**معلومات المصدر (الوصف الكامل للمود و/أو الميزات الرئيسية):**
{primary_context_source}

**التعليمات:**
- **استخدم 'معلومات المصدر' المقدمة (التي تحتوي على الوصف الكامل للمود و/أو الميزات المقدمة من المستخدم) كأساس رئيسي لكتابتك.**
- **أنشئ وصفاً جديداً وفريداً وجذاباً.** لا تقم بنسخ أو إعادة صياغة الوصف الأصلي ببساطة.
- **يجب أن تذكر صراحة الميزات الرئيسية للمود** ضمن وصفك الجديد. ارجع إلى 'معلومات المصدر' لتحديد هذه الميزات.
- سلط الضوء على قيمة المحتوى للاعبين وكيف يحسن تجربة اللعب.
- استخدم اللغة العربية الطبيعية والسلسة. تجنب التكرار.
- يجب أن يكون الناتج باللغة العربية فقط.
- لا تضع اسم المحتوى أو النوع في البداية إلا إذا كان ذلك مناسباً طبيعياً.
- لا تضف أي عناوين مثل "الوصف:" أو ما شابه؛ قدم النص الوصفي فقط.
- إذا كانت 'معلومات المصدر' قليلة، ركز على ما هو متوفر لإنشاء أفضل وصف ممكن، مع التأكد من ذكر الميزات إذا كانت متوفرة.

قدم إجابتك بهذا التنسيق بالضبط:
[ARABIC_DESCRIPTION]
وصفك الجذاب باللغة العربية هنا...
[/ARABIC_DESCRIPTION]
"""
        
        print("\n📤 إرسال الطلب إلى Gemini لإنشاء الوصف العربي...")
        start_time = time.time()
        
        response = model.generate_content(prompt)
        full_response = response.text.strip()
        
        end_time = time.time()
        print(f"📥 تم استلام الرد من Gemini للوصف العربي في {end_time - start_time:.2f} ثانية")
        
        # Extract Arabic description (like in the real function)
        import re
        desc_match = re.search(r'\[ARABIC_DESCRIPTION\](.*?)\[/ARABIC_DESCRIPTION\]', full_response, re.DOTALL)
        if desc_match:
            generated_arabic_desc = desc_match.group(1).strip()
            print(f"✅ تم استخراج الوصف العربي بنجاح ({len(generated_arabic_desc)} حرف)")
            print("="*60)
            print("📝 الوصف العربي المُنشأ:")
            print("="*60)
            print(generated_arabic_desc)
            print("="*60)
            
            # Test that it contains Arabic text
            arabic_chars = sum(1 for char in generated_arabic_desc if '\u0600' <= char <= '\u06FF')
            if arabic_chars > 10:
                print(f"✅ الوصف يحتوي على {arabic_chars} حرف عربي - جيد!")
            else:
                print(f"⚠️ تحذير: الوصف يحتوي على {arabic_chars} حرف عربي فقط")
                
        else:
            print("⚠️ تحذير: لم يتبع Gemini التنسيق المتوقع")
            print("الرد الكامل:")
            print("="*60)
            print(full_response)
            print("="*60)
            
    except Exception as e:
        print(f"❌ Gemini test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
else:
    if not api_keys:
        print("❌ No API keys found")
    if not GEMINI_AVAILABLE:
        print("❌ Gemini library not available")

print("\n" + "="*60)
print("🎯 AUTOMATIC ARABIC DESCRIPTION TEST COMPLETE")
print("="*60)
print("\n📋 Summary:")
print("- This test simulates the automatic Arabic description generation")
print("- It uses the same prompt and logic as the real mod_processor.py")
print("- If successful, the feature should work in the main application")
print("- The Arabic description should appear automatically after data extraction")
print("\n🚀 Next steps:")
print("1. Run the main application: python mod_processor.py")
print("2. Extract data from a mod URL")
print("3. Watch for Arabic description generation messages")
print("4. Check the 'Arabic Description' field in the publish section")
