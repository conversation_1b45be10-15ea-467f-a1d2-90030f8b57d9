"""
الملف الرئيسي لأداة استخراج بيانات مودات mcpedl.com
"""
import sys
import argparse
import logging
from typing import List, Optional
import time

from config import Config
from utils import setup_logging, is_valid_mcpedl_url
from scraper import MCPEDLScraper
from supabase_client import SupabaseClient
from llm_generator import LLMDescriptionGenerator

class MCPEDLModExtractor:
    """الكلاس الرئيسي لاستخراج ومعالجة بيانات المودات"""
    
    def __init__(self):
        self.logger = setup_logging()
        self.scraper = MCPEDLScraper()
        self.supabase_client = None
        self.llm_generator = None
        
        # تهيئة العملاء
        self._initialize_clients()
    
    def _initialize_clients(self):
        """تهيئة عملاء قاعدة البيانات ومولد الأوصاف"""
        try:
            # التحقق من صحة الإعدادات
            Config.validate_config()
            
            # تهيئة عميل Supabase
            self.supabase_client = SupabaseClient()
            self.logger.info("تم تهيئة عميل Supabase")
            
            # تهيئة مولد الأوصاف
            self.llm_generator = LLMDescriptionGenerator()
            self.logger.info("تم تهيئة مولد الأوصاف")
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة العملاء: {e}")
            raise
    
    def process_single_mod(self, url: str, skip_existing: bool = True) -> bool:
        """معالجة مود واحد"""
        self.logger.info(f"بدء معالجة المود: {url}")
        
        # التحقق من صحة الرابط
        if not is_valid_mcpedl_url(url):
            self.logger.error(f"رابط غير صحيح: {url}")
            return False
        
        # التحقق من وجود المود مسبقاً
        if skip_existing and self.supabase_client.mod_exists(url):
            self.logger.info(f"المود موجود مسبقاً، سيتم تخطيه: {url}")
            return True
        
        try:
            # استخراج بيانات المود
            mod_data = self.scraper.scrape_mod_data(url)
            if not mod_data:
                self.logger.error(f"فشل في استخراج بيانات المود: {url}")
                return False
            
            # تحسين البيانات بإضافة الأوصاف المولدة
            if self.llm_generator:
                mod_data = self.llm_generator.enhance_mod_data(mod_data)
            
            # حفظ البيانات في قاعدة البيانات
            result = self.supabase_client.upsert_mod(mod_data)
            if result:
                self.logger.info(f"تم حفظ المود بنجاح: {mod_data['name']}")
                return True
            else:
                self.logger.error(f"فشل في حفظ المود: {mod_data['name']}")
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في معالجة المود {url}: {e}")
            return False
    
    def process_multiple_mods(self, urls: List[str], skip_existing: bool = True) -> dict:
        """معالجة عدة مودات"""
        results = {
            'total': len(urls),
            'successful': 0,
            'failed': 0,
            'skipped': 0
        }
        
        self.logger.info(f"بدء معالجة {len(urls)} مود")
        
        for i, url in enumerate(urls, 1):
            self.logger.info(f"معالجة المود {i}/{len(urls)}: {url}")
            
            try:
                success = self.process_single_mod(url, skip_existing)
                if success:
                    results['successful'] += 1
                else:
                    results['failed'] += 1
                
                # تأخير بين المعالجات لتجنب الحظر
                if i < len(urls):
                    time.sleep(Config.REQUEST_DELAY)
                    
            except Exception as e:
                self.logger.error(f"خطأ في معالجة المود {url}: {e}")
                results['failed'] += 1
        
        self.logger.info(f"انتهت المعالجة - نجح: {results['successful']}, فشل: {results['failed']}")
        return results
    
    def process_from_file(self, file_path: str, skip_existing: bool = True) -> dict:
        """معالجة مودات من ملف نصي"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                urls = [line.strip() for line in f if line.strip() and is_valid_mcpedl_url(line.strip())]
            
            if not urls:
                self.logger.error("لم يتم العثور على روابط صحيحة في الملف")
                return {'total': 0, 'successful': 0, 'failed': 0, 'skipped': 0}
            
            return self.process_multiple_mods(urls, skip_existing)
            
        except FileNotFoundError:
            self.logger.error(f"الملف غير موجود: {file_path}")
            return {'total': 0, 'successful': 0, 'failed': 0, 'skipped': 0}
        except Exception as e:
            self.logger.error(f"خطأ في قراءة الملف: {e}")
            return {'total': 0, 'successful': 0, 'failed': 0, 'skipped': 0}
    
    def close(self):
        """إغلاق الموارد"""
        if self.scraper:
            self.scraper.close()

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(
        description='أداة استخراج بيانات مودات Minecraft من mcpedl.com',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
  python main.py --url "https://mcpedl.com/dragon-mounts-v1-3-25/"
  python main.py --file urls.txt
  python main.py --url "https://mcpedl.com/mod-name/" --no-skip-existing
        """
    )
    
    parser.add_argument(
        '--url', 
        type=str, 
        help='رابط صفحة المود المراد استخراج بياناته'
    )
    
    parser.add_argument(
        '--file', 
        type=str, 
        help='ملف نصي يحتوي على روابط المودات (رابط واحد في كل سطر)'
    )
    
    parser.add_argument(
        '--no-skip-existing', 
        action='store_true', 
        help='عدم تخطي المودات الموجودة مسبقاً (تحديث البيانات)'
    )
    
    parser.add_argument(
        '--verbose', 
        action='store_true', 
        help='عرض تفاصيل أكثر في السجلات'
    )
    
    args = parser.parse_args()
    
    # إعداد مستوى التسجيل
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # التحقق من وجود مدخلات
    if not args.url and not args.file:
        parser.print_help()
        sys.exit(1)
    
    # تهيئة المستخرج
    try:
        extractor = MCPEDLModExtractor()
    except Exception as e:
        print(f"خطأ في تهيئة المستخرج: {e}")
        sys.exit(1)
    
    try:
        skip_existing = not args.no_skip_existing
        
        if args.url:
            # معالجة مود واحد
            success = extractor.process_single_mod(args.url, skip_existing)
            if success:
                print("تم استخراج ومعالجة المود بنجاح!")
            else:
                print("فشل في معالجة المود")
                sys.exit(1)
        
        elif args.file:
            # معالجة مودات من ملف
            results = extractor.process_from_file(args.file, skip_existing)
            print(f"\nنتائج المعالجة:")
            print(f"إجمالي المودات: {results['total']}")
            print(f"نجح: {results['successful']}")
            print(f"فشل: {results['failed']}")
            
            if results['failed'] > 0:
                sys.exit(1)
    
    finally:
        extractor.close()

if __name__ == "__main__":
    main()
