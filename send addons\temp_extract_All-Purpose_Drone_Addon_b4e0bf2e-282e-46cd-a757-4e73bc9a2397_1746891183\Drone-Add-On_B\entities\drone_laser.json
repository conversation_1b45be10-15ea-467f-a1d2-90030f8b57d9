{"format_version": "1.16.100", "minecraft:entity": {"description": {"identifier": "drone:laser", "runtime_identifier": "minecraft:arrow", "is_spawnable": false, "is_summonable": true, "is_experimental": false}, "component_groups": {"despawn": {"minecraft:instant_despawn": {}}}, "components": {"minecraft:type_family": {"family": ["laser", "drone"]}, "minecraft:scale": {"value": 1}, "minecraft:timer": {"time": [1.5, 1.5], "looping": true, "time_down_event": {"event": "despawn"}}, "minecraft:collision_box": {"width": 0.25, "height": 0.25}, "minecraft:projectile": {"on_hit": {"impact_damage": {"damage": [3, 5], "knockback": true, "semi_random_diff_damage": false, "destroy_on_hit": true}, "remove_on_hit": {}}, "power": 2, "gravity": 0.002, "liquid_inertia": 1, "uncertainty_base": 12, "uncertainty_multiplier": 0, "anchor": 1, "should_bounce": false, "offset": [0, 0, -1]}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {"default_values": {"max_optimized_distance": 80.0, "max_dropped_ticks": 7, "use_motion_prediction_hints": true}}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": false}}, "events": {"despawn": {"add": {"component_groups": ["despawn"]}}}}}