#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لاستخراج MCPEDL مع التحسينات الجديدة
"""

import sys
import os

# إضافة مجلد send addons إلى المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
addons_dir = os.path.join(current_dir, 'send addons')
sys.path.insert(0, addons_dir)

def test_quick_extraction():
    """اختبار سريع للاستخراج"""
    print("🚀 اختبار سريع لاستخراج MCPEDL")
    print("=" * 50)
    
    try:
        from mcpedl_selenium_scraper import scrape_mcpedl_with_selenium
        
        # رابط للاختبار
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        
        print(f"🔗 الرابط: {test_url}")
        print("⏳ جاري الاستخراج...")
        
        # استخراج البيانات
        mod_data = scrape_mcpedl_with_selenium(test_url)
        
        if mod_data:
            print("\n🎉 نجح الاستخراج!")
            print("=" * 50)
            
            # عرض النتائج
            name = mod_data.get('name', 'غير متوفر')
            description = mod_data.get('description', '')
            description_arabic = mod_data.get('description_arabic', '')
            images = mod_data.get('image_urls', [])
            creator = mod_data.get('creator_name', '')
            social_channels = mod_data.get('creator_social_channels', [])
            
            print(f"📝 الاسم: {name}")
            print(f"📏 طول الاسم: {len(name)} حرف")
            
            # فحص الاختصار
            if ':' not in name and 'Community Edition' not in name:
                print("✅ الاسم مختصر بشكل صحيح")
            else:
                print("⚠️ الاسم لم يتم اختصاره")
            
            print(f"\n📄 الوصف الإنجليزي: {len(description)} حرف")
            if description:
                print(f"   البداية: {description[:100]}...")
                
                # فحص جودة الوصف
                if len(description) > 200 and 'Dragon' in description:
                    print("✅ وصف إنجليزي مفصل وذكي")
                else:
                    print("⚠️ وصف إنجليزي قصير أو عام")
            
            print(f"\n📄 الوصف العربي: {len(description_arabic)} حرف")
            if description_arabic:
                print(f"   البداية: {description_arabic[:100]}...")
                
                # فحص النص العربي
                has_arabic = any(ord(char) > 1536 and ord(char) < 1791 for char in description_arabic)
                if has_arabic and len(description_arabic) > 200:
                    print("✅ وصف عربي مفصل وصحيح")
                else:
                    print("⚠️ وصف عربي قصير أو غير صحيح")
            else:
                print("❌ لا يوجد وصف عربي")
            
            print(f"\n🖼️ الصور: {len(images)} صورة")
            if images:
                valid_images = 0
                for i, img in enumerate(images[:5]):
                    print(f"   [{i+1}]: {img[:60]}...")
                    
                    # فحص جودة الصور
                    if not any(bad in img.lower() for bad in ['shield.png', '/users/', 'data:image']):
                        valid_images += 1
                        print("      ✅ صورة صالحة")
                    else:
                        print("      ❌ صورة ثابتة")
                
                print(f"   📊 الصور الصالحة: {valid_images}/{len(images)}")
                
                if valid_images >= len(images) * 0.8:
                    print("✅ معظم الصور صالحة")
                else:
                    print("⚠️ كثير من الصور الثابتة")
            else:
                print("❌ لا توجد صور")
            
            print(f"\n👤 المطور: {creator}")
            if creator:
                # فحص تنظيف اسم المطور
                if not any(unwanted in creator for unwanted in ['Published', 'Updated', 'CurseForge', '2024', '2025']):
                    print("✅ اسم المطور نظيف")
                else:
                    print("⚠️ اسم المطور يحتوي على نصوص إضافية")
            else:
                print("❌ اسم المطور مفقود")
            
            print(f"\n🔗 قنوات التواصل: {len(social_channels)} قناة")
            if social_channels:
                valid_channels = 0
                for channel in social_channels[:3]:
                    print(f"   🔗 {channel}")
                    
                    # فحص جودة القنوات
                    if not any(bad in channel for bad in ['data:image', 'mcpedl.com']):
                        valid_channels += 1
                        print("      ✅ قناة صالحة")
                    else:
                        print("      ❌ قناة ثابتة")
                
                print(f"   📊 القنوات الصالحة: {valid_channels}/{len(social_channels)}")
                
                if valid_channels >= len(social_channels) * 0.8:
                    print("✅ معظم القنوات صالحة")
                else:
                    print("⚠️ كثير من القنوات الثابتة")
            else:
                print("❌ لا توجد قنوات تواصل")
            
            # تقييم عام
            print("\n" + "=" * 50)
            print("📊 التقييم العام:")
            
            score = 0
            total_checks = 6
            
            # فحص الاسم
            if name and ':' not in name:
                score += 1
                print("✅ اسم مختصر")
            
            # فحص الوصف الإنجليزي
            if description and len(description) > 200:
                score += 1
                print("✅ وصف إنجليزي مفصل")
            
            # فحص الوصف العربي
            if description_arabic and len(description_arabic) > 200:
                score += 1
                print("✅ وصف عربي مفصل")
            
            # فحص الصور
            if images and len(images) >= 3:
                score += 1
                print("✅ صور كافية")
            
            # فحص المطور
            if creator and len(creator) > 2:
                score += 1
                print("✅ اسم المطور")
            
            # فحص قنوات التواصل
            if social_channels and len(social_channels) >= 1:
                score += 1
                print("✅ قنوات تواصل")
            
            print(f"\n🎯 النتيجة: {score}/{total_checks}")
            
            if score == total_checks:
                print("🎉 ممتاز! جميع التحسينات تعمل بشكل مثالي!")
            elif score >= total_checks * 0.8:
                print("👍 جيد جداً! معظم التحسينات تعمل")
            elif score >= total_checks * 0.6:
                print("👌 جيد! بعض التحسينات تعمل")
            else:
                print("⚠️ يحتاج تحسين")
            
            return True
        else:
            print("❌ فشل الاستخراج")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار سريع للتحسينات الجديدة")
    print("=" * 60)
    print("هذا الاختبار يتحقق من:")
    print("- ✅ اختصار اسم المود")
    print("- ✅ فصل الوصف العربي والإنجليزي")
    print("- ✅ فلترة الصور الثابتة")
    print("- ✅ فلترة روابط التواصل الثابتة")
    print("- ✅ تنظيف اسم المطور")
    print()
    
    success = test_quick_extraction()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 الاختبار نجح!")
        print("💡 يمكنك الآن استخدام الأداة الرئيسية:")
        print('   python "send addons/mod_processor.py"')
    else:
        print("❌ الاختبار فشل")
        print("💡 جرب البيانات التجريبية:")
        print("   python demo_mcpedl_data.py")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        input("\nاضغط Enter للخروج...")
