{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:piglin", "materials": {"default": "phantom"}, "textures": {"default": "textures/entity/piglin/piglin"}, "geometry": {"default": "geometry.piglin"}, "spawn_egg": {"base_color": "#995f40", "overlay_color": "#f9f3a4"}, "scripts": {"pre_animation": ["variable.tcos0 = (Math.cos(query.modified_distance_moved * 38.17) * query.modified_move_speed / variable.gliding_speed_value) * 57.3;", "variable.attack = Math.sin((1.0 - (1.0 - variable.attack_time) * (1.0 - variable.attack_time)) * 180.0) * 57.3;", "variable.attack2 = Math.sin(variable.attack_time * 180.0) * 57.3;", "variable.z_bob = Math.cos(query.life_time * 103.13244) * 2.865 + 2.865;", "variable.x_bob = Math.sin(query.life_time * 76.776372) * 2.865;"], "animate": [{"admire": "query.is_admiring"}, {"humanoid_big_head": "query.is_baby"}, {"celebrate_hunt": "query.is_celebrating"}, {"celebrate_hunt_special": "query.is_celebrating_special"}, "move", "bob", "look_at_target_controller", "piglin_attack_controller", "riding_controller"]}, "animations": {"move": "animation.piglin.move", "bob": "animation.humanoid.bob", "humanoid_big_head": "animation.humanoid.big_head", "admire": "animation.piglin.admire", "celebrate_hunt": "animation.piglin.celebrate_hunt", "celebrate_hunt_special": "animation.piglin.celebrate_hunt_special", "look_at_target_controller": "controller.animation.humanoid.look_at_target", "look_at_target_default": "animation.humanoid.look_at_target.default", "look_at_target_gliding": "animation.humanoid.look_at_target.gliding", "look_at_target_swimming": "animation.humanoid.look_at_target.swimming", "piglin_attack_controller": "controller.animation.piglin.attack", "crossbow_hold": "animation.piglin.crossbow.hold", "crossbow_charge": "animation.piglin.crossbow.charge", "melee_attack": "animation.piglin.sword.attack", "hand_attack": "animation.piglin.hand.attack", "riding_controller": "controller.animation.humanoid.riding", "riding.arms": "animation.humanoid.riding.arms", "riding.legs": "animation.humanoid.riding.legs"}, "render_controllers": ["controller.render.piglin"], "enable_attachables": true}}}