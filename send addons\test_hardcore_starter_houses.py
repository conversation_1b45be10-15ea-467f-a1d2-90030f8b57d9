# -*- coding: utf-8 -*-
"""
اختبار صفحة Hardcore Starter Houses لفهم مشكلة استخراج الصور
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_hardcore_starter_houses():
    """اختبار صفحة Hardcore Starter Houses المحددة"""
    print("🧪 اختبار صفحة Hardcore Starter Houses")
    print("=" * 60)
    
    test_url = "https://mcpedl.com/hardcore-starter-houses/"
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        import cloudscraper
        
        print(f"🔗 رابط الاختبار: {test_url}")
        print()
        
        # جلب الصفحة
        print("🌐 جلب الصفحة...")
        scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'windows',
                'mobile': False
            }
        )
        
        response = scraper.get(test_url, timeout=30)
        
        if response.status_code == 200:
            print(f"✅ تم جلب الصفحة بنجاح ({len(response.text)} حرف)")
            
            # تحليل محتوى الصفحة
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # فحص بنية الصفحة
            print("\n🔍 تحليل بنية الصفحة:")
            
            # فحص العنوان
            title = soup.find('title')
            if title:
                print(f"   📝 العنوان: {title.get_text().strip()}")
            
            # فحص og:image
            og_image = soup.find('meta', property='og:image')
            if og_image:
                print(f"   🖼️ og:image: {og_image.get('content', 'غير موجود')}")
            else:
                print("   ❌ لا يوجد og:image")
            
            # فحص جميع الصور في الصفحة
            all_images = soup.find_all('img')
            print(f"\n📊 إجمالي وسوم <img> في الصفحة: {len(all_images)}")
            
            if all_images:
                print("   📋 قائمة جميع الصور:")
                for i, img in enumerate(all_images, 1):
                    src = img.get('src') or img.get('data-src') or 'لا يوجد src'
                    alt = img.get('alt', 'لا يوجد alt')
                    print(f"      [{i:2d}] {src}")
                    print(f"           alt: {alt}")
                    
                    # تحليل نوع الصورة
                    if '/img/empty.png' in src:
                        print(f"           🔍 نوع: صورة فارغة (placeholder)")
                    elif 'gravatar.com' in src:
                        print(f"           🔍 نوع: صورة مستخدم (gravatar)")
                    elif 'r2.mcpedl.com/users' in src:
                        print(f"           🔍 نوع: صورة مستخدم (mcpedl)")
                    elif '_nuxt' in src:
                        print(f"           🔍 نوع: صورة موقع (nuxt)")
                    elif 'media.forgecdn.net' in src:
                        print(f"           🔍 نوع: صورة مود حقيقية (forgecdn)")
                    else:
                        print(f"           🔍 نوع: أخرى")
            
            # اختبار المستخرج المحسن
            print("\n🚀 اختبار المستخرج المحسن:")
            extractor = MCPEDLExtractorFixed()
            
            # استخراج البيانات
            mod_data = extractor.extract_mod_data(response.text, test_url, generate_ai_descriptions=False)
            
            if mod_data:
                print("\n📊 نتائج الاستخراج:")
                print(f"   📝 الاسم: {mod_data.get('name', 'غير محدد')}")
                print(f"   📂 الفئة: {mod_data.get('category', 'غير محدد')}")
                print(f"   👤 المطور: {mod_data.get('creator_name', 'غير محدد')}")
                print(f"   📦 الإصدار: {mod_data.get('version', 'غير محدد')}")
                
                # تفاصيل الصور
                images = mod_data.get('image_urls', [])
                print(f"\n🖼️ الصور المستخرجة: {len(images)} صورة")
                
                if images:
                    print("   📋 قائمة الصور المستخرجة:")
                    for i, img_url in enumerate(images, 1):
                        print(f"      [{i:2d}] {img_url}")
                else:
                    print("   ❌ لم يتم استخراج أي صور")
                
                # تحليل السبب
                print(f"\n🔍 تحليل السبب:")
                if len(images) == 0:
                    print("   💡 الأسباب المحتملة لعدم وجود صور:")
                    print("      - هذه صفحة خريطة (Map) وليس مود")
                    print("      - الصفحة تحتوي على ملفات تحميل فقط")
                    print("      - لا توجد صور مرفقة من forgecdn")
                    print("      - الصور موجودة في ملفات .mcworld المضغوطة")
                    
                    # فحص روابط التحميل
                    download_links = soup.find_all('a', href=True)
                    mcworld_links = [link for link in download_links if '.mcworld' in link.get('href', '')]
                    
                    print(f"   📦 روابط .mcworld الموجودة: {len(mcworld_links)}")
                    if mcworld_links:
                        print("      📋 أول 3 روابط:")
                        for i, link in enumerate(mcworld_links[:3], 1):
                            href = link.get('href', '')
                            text = link.get_text().strip()
                            print(f"         [{i}] {text}: {href}")
                
                return True
            else:
                print("❌ فشل في استخراج البيانات")
                return False
        else:
            print(f"❌ فشل في جلب الصفحة: {response.status_code}")
            return False
            
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_with_images_page():
    """اختبار صفحة تحتوي على صور فعلية"""
    print("\n" + "=" * 60)
    print("🧪 اختبار صفحة تحتوي على صور فعلية")
    print("=" * 60)
    
    # صفحات للاختبار تحتوي على صور
    test_urls = [
        "https://mcpedl.com/dragon-mounts/",
        "https://mcpedl.com/mo-dungeons-addon/",
        "https://mcpedl.com/parkour-add-on-by-s7d/"
    ]
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        import cloudscraper
        
        extractor = MCPEDLExtractorFixed()
        scraper = cloudscraper.create_scraper()
        
        for i, test_url in enumerate(test_urls, 1):
            print(f"\n🔗 [{i}] اختبار: {test_url}")
            
            try:
                response = scraper.get(test_url, timeout=20)
                
                if response.status_code == 200:
                    mod_data = extractor.extract_mod_data(response.text, test_url, generate_ai_descriptions=False)
                    
                    if mod_data:
                        images = mod_data.get('image_urls', [])
                        print(f"   🖼️ الصور: {len(images)}")
                        
                        if images:
                            print(f"   ✅ نجح! أول صورة: {images[0][:60]}...")
                        else:
                            print(f"   ⚠️ لا توجد صور")
                    else:
                        print(f"   ❌ فشل الاستخراج")
                else:
                    print(f"   ❌ فشل الجلب: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ خطأ: {e}")
                
    except Exception as e:
        print(f"❌ خطأ عام: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار شامل لمشكلة استخراج الصور من MCPEDL")
    print("=" * 80)
    
    # اختبار الصفحة المحددة
    success1 = test_hardcore_starter_houses()
    
    # اختبار صفحات أخرى
    test_with_images_page()
    
    print("\n" + "=" * 80)
    print("📊 ملخص النتائج:")
    print(f"   🎯 اختبار Hardcore Starter Houses: {'✅ نجح' if success1 else '❌ فشل'}")
    print("\n💡 الخلاصة:")
    print("   - بعض صفحات MCPEDL لا تحتوي على صور مرفقة")
    print("   - هذا طبيعي للخرائط والملفات المضغوطة")
    print("   - المستخرج يعمل بشكل صحيح ويوضح السبب")

if __name__ == "__main__":
    main()
