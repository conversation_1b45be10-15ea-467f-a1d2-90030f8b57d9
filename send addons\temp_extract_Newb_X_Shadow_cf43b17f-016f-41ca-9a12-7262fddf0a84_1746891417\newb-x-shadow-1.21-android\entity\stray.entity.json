{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:stray", "min_engine_version": "1.8.0", "materials": {"default": "spider", "overlay": "stray_clothes"}, "textures": {"default": "textures/entity/skeleton/stray", "overlay": "textures/entity/skeleton/stray_overlay"}, "geometry": {"default": "geometry.skeleton.stray.v1.8", "overlay": "geometry.stray.armor.v1.8"}, "spawn_egg": {"texture": "spawn_egg", "texture_index": 27}, "scripts": {"pre_animation": ["variable.tcos0 = (Math.cos(query.modified_distance_moved * 38.17) * query.modified_move_speed / variable.gliding_speed_value) * 57.3;"]}, "animations": {"look_at_target_default": "animation.humanoid.look_at_target.default", "look_at_target_gliding": "animation.humanoid.look_at_target.gliding", "look_at_target_swimming": "animation.humanoid.look_at_target.swimming", "move": "animation.humanoid.move", "riding.arms": "animation.humanoid.riding.arms", "riding.legs": "animation.humanoid.riding.legs", "holding": "animation.humanoid.holding", "brandish_spear": "animation.humanoid.brandish_spear", "charging": "animation.humanoid.charging", "attack.rotations": "animation.humanoid.attack.rotations", "sneaking": "animation.humanoid.sneaking", "bob": "animation.humanoid.bob", "damage_nearby_mobs": "animation.humanoid.damage_nearby_mobs", "bow_and_arrow": "animation.humanoid.bow_and_arrow", "swimming": "animation.zombie.swimming", "use_item_progress": "animation.humanoid.use_item_progress", "skeleton_attack": "animation.skeleton.attack"}, "animation_controllers": [{"look_at_target": "controller.animation.humanoid.look_at_target"}, {"move": "controller.animation.humanoid.move"}, {"riding": "controller.animation.humanoid.riding"}, {"holding": "controller.animation.humanoid.holding"}, {"brandish_spear": "controller.animation.humanoid.brandish_spear"}, {"charging": "controller.animation.humanoid.charging"}, {"attack": "controller.animation.humanoid.attack"}, {"sneaking": "controller.animation.humanoid.sneaking"}, {"bob": "controller.animation.humanoid.bob"}, {"damage_nearby_mobs": "controller.animation.humanoid.damage_nearby_mobs"}, {"bow_and_arrow": "controller.animation.humanoid.bow_and_arrow"}, {"swimming": "controller.animation.zombie.swimming"}, {"use_item_progress": "controller.animation.humanoid.use_item_progress"}, {"skeleton_attack": "controller.animation.skeleton.attack"}], "render_controllers": ["controller.render.stray_clothes", "controller.render.stray"], "enable_attachables": true}}}