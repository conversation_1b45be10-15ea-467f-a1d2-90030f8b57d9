# تحسينات أداة نشر مودات ماين كرافت - النظام الجديد للمواقع المخصصة

## التحسينات الجديدة المضافة

### 1. تحسين Gemini AI لكشف المواقع المخصصة

تم تحسين نظام Gemini AI ليتمكن من:
- **تجنب روابط الموقع نفسه**: يتجنب Gemini الآن روابط التواصل الاجتماعي الخاصة بالموقع (مثل أزرار المشاركة في أعلى الصفحة)
- **التركيز على صانع المود**: يركز فقط على معلومات التواصل الخاصة بصانع المود
- **كشف المواقع المخصصة**: يكتشف المنصات المخصصة مثل خوادم Discord، قنوات YouTube، المنتديات الشخصية
- **استخراج رسائل التواصل**: يستخرج رسائل مثل "اشترك في قناتي" أو "انضم لخادم Discord الخاص بي"

### 2. واجهة مستخدم محسنة للمواقع المتعددة

تم استبدال الحقول الثابتة بنظام ديناميكي:

#### إزالة مربع قنوات التواصل الاجتماعي القديم
- تم إزالة المربع النصي الثابت للقنوات الاجتماعية

#### نظام المواقع المخصصة الجديد المحسن
- **إضافة مواقع متعددة**: يمكن إضافة عدد غير محدود من المواقع المخصصة
- **حقول ديناميكية**: كل موقع له حقل اسم وحقل رابط منفصل
- **أزرار لصق ومسح**: كل حقل له زر "لصق" و زر "مسح" منفصل
- **أزرار إدارة**: زر "إضافة موقع" و زر "حذف" لكل موقع
- **ملء تلقائي محسن**: يتم ملء الحقول تلقائياً من بيانات Gemini مع إدارة ذكية للصفوف
- **تخطيط محسن**: تنسيق أفضل مع مساحات مناسبة بين الأزرار

### 3. تحديث قاعدة البيانات

**لا حاجة لتحديث قاعدة البيانات!**
- النظام الجديد يستخدم العمود الموجود `creator_social_channels` لحفظ جميع المواقع المخصصة
- تنسيق البيانات: `["اسم الموقع: الرابط", "موقع آخر: رابط آخر"]`
- متوافق مع البيانات الموجودة

### 4. التكامل التلقائي المحسن

#### استخراج ذكي بواسطة Gemini
- **تجنب روابط الموقع**: يتجنب Gemini روابط المشاركة والتواصل الخاصة بالموقع نفسه
- **كشف رسائل التواصل**: يستخرج رسائل صانع المود مثل "للدعم، انضم لخادم Discord الخاص بي"
- **ملء تلقائي للحقول**: يملأ الحقول المتعددة تلقائياً بالمواقع المكتشفة
- **تحليل محتوى المقال**: يركز على المحتوى الرئيسي وليس العناصر الجانبية

#### نظام الحفظ الجديد
- **تجميع المواقع**: يتم تجميع جميع المواقع المخصصة في قائمة واحدة
- **تنسيق موحد**: "اسم الموقع: الرابط" لكل موقع
- **حفظ في JSONB**: يتم حفظ البيانات في العمود الموجود `creator_social_channels`

## كيفية الاستخدام

### 1. إعداد قاعدة البيانات

**لا حاجة لأي تحديث!** النظام الجديد متوافق مع قاعدة البيانات الحالية.

إذا كنت تريد إنشاء جدول جديد:
```bash
# تشغيل ملف إنشاء الجدول الكامل
psql -f create_mods_table.sql
```

#### تنظيف قاعدة البيانات (اختياري):
```bash
# تشغيل ملف تنظيف البيانات غير المهمة
psql -f cleanup_mods_table.sql
```

**ملاحظة**: ملف التنظيف يحذف:
- المودات بدون تحميلات أو إعجابات لأكثر من 30 يوم
- المودات بأسماء فارغة أو تجريبية
- المودات بروابط تحميل غير صالحة
- المودات المكررة
- البيانات القديمة غير المستخدمة

### 2. استخدام النظام الجديد

#### الاستخراج التلقائي المحسن:
1. أدخل رابط صفحة المود في حقل "رابط المقال"
2. اضغط على "استخراج البيانات"
3. سيقوم Gemini بملء الحقول تلقائياً:
   - اسم صانع المود
   - معلومات التواصل (رسائل مثل "انضم لخادم Discord")
   - المواقع المخصصة (Discord، YouTube، إلخ)

#### الإدخال اليدوي للمواقع المتعددة:
1. في قسم "مواقع التواصل المخصصة"
2. املأ الحقل الأول: اسم الموقع (مثل "خادم Discord")
   - استخدم زر "لصق" لنسخ النص من الحافظة
   - استخدم زر "مسح" لمسح الحقل
3. املأ الحقل الثاني: رابط الموقع
   - استخدم زر "لصق" لنسخ الرابط من الحافظة
   - استخدم زر "مسح" لمسح الحقل
4. اضغط "إضافة موقع" لإضافة موقع آخر
5. كرر العملية لإضافة مواقع متعددة
6. استخدم زر "حذف" لإزالة أي موقع
7. اضغط على "نشر المود الآن"

#### مميزات الأزرار الجديدة:
- **أزرار لصق**: تنسخ النص من الحافظة مباشرة
- **أزرار مسح**: تمسح محتوى الحقل بنقرة واحدة
- **إدارة ذكية**: الأزرار تعمل بشكل مستقل لكل حقل
- **تخطيط محسن**: ترتيب منطقي للأزرار مع مساحات مناسبة

### 3. أمثلة على المواقع المخصصة

- **منتديات مخصصة**: "منتدى المطور" → "https://developer-forum.com"
- **قنوات التليجرام**: "قناة التليجرام" → "https://t.me/developer_channel"
- **مدونات شخصية**: "مدونة المطور" → "https://developer-blog.com"
- **مجتمعات Reddit**: "مجتمع Reddit" → "https://reddit.com/r/developer_community"
- **خوادم Discord مخصصة**: "خادم Discord المخصص" → "https://discord.gg/custom_server"

## التحسينات التقنية

### 1. تحديث Prompt الخاص بـ Gemini
- إضافة قسم `unknown_social_sites` في JSON المُستخرج
- تعليمات مفصلة لكشف المنصات غير المعروفة
- دعم استخراج اسم المنصة والرابط معاً

### 2. تحديث واجهة المستخدم
- إضافة حقلين جديدين مع أزرار لصق ومسح
- تحديث دالة مسح الحقول لتشمل الحقول الجديدة
- إضافة الحقول الجديدة لقائمة أزرار اللصق

### 3. تحديث منطق النشر
- دمج المعلومات المخصصة مع قنوات التواصل الموجودة
- حفظ البيانات في الأعمدة الجديدة في قاعدة البيانات
- التعامل مع القيم الفارغة بشكل صحيح

### 4. تحديث دالة تحديث GUI
- معالجة البيانات المُستخرجة من `unknown_social_sites`
- ملء الحقول المخصصة تلقائياً
- إضافة المواقع الإضافية لقائمة قنوات التواصل

## الملفات المُحدثة

1. **mod_processor.py**: الملف الرئيسي مع جميع التحسينات
2. **create_mods_table.sql**: ملف إنشاء جدول جديد كامل
3. **update_mods_table.sql**: ملف تحديث الجدول الموجود
4. **README_IMPROVEMENTS.md**: هذا الملف للتوثيق

## ملاحظات مهمة

- تأكد من تحديث قاعدة البيانات قبل استخدام الميزات الجديدة
- الحقول الجديدة اختيارية ولن تؤثر على المودات الموجودة
- يمكن استخدام الحقول الجديدة مع أو بدون الاستخراج التلقائي
- النظام متوافق مع الإصدارات السابقة

## استكشاف الأخطاء

### إذا لم تظهر الحقول الجديدة:
1. تأكد من تشغيل ملف تحديث قاعدة البيانات
2. أعد تشغيل التطبيق
3. تحقق من أن الحقول موجودة في قاعدة البيانات

### إذا لم يعمل الاستخراج التلقائي:
1. تأكد من تكوين مفاتيح Gemini API بشكل صحيح
2. تحقق من اتصال الإنترنت
3. راجع سجل الحالة للأخطاء

### إذا فشل النشر:
1. تأكد من وجود الأعمدة الجديدة في قاعدة البيانات
2. تحقق من صلاحيات قاعدة البيانات
3. راجع رسائل الخطأ في سجل الحالة
