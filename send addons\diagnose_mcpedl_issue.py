#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة استخراج MCPEDL وتحليل HTML المستلم
"""

import os
import requests
import time
from bs4 import BeautifulSoup

def analyze_html_file():
    """تحليل ملف HTML المحفوظ للتشخيص"""
    html_files = ['debug_mcpedl_page.html', 'debug_selenium_page.html']
    
    for html_file in html_files:
        if os.path.exists(html_file):
            print(f"\n📄 تحليل ملف: {html_file}")
            print("=" * 50)
            
            try:
                with open(html_file, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                print(f"📏 حجم الملف: {len(html_content)} حرف")
                
                # تحليل HTML
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # العنوان
                title = soup.find('title')
                if title:
                    print(f"📝 العنوان: {title.get_text()[:100]}...")
                else:
                    print("❌ لا يوجد عنوان")
                
                # البحث عن علامات الحماية
                protection_keywords = ['cloudflare', 'ddos protection', 'access denied', 'blocked', 'captcha', 'challenge']
                found_protection = []
                
                for keyword in protection_keywords:
                    if keyword in html_content.lower():
                        found_protection.append(keyword)
                
                if found_protection:
                    print(f"🛡️ علامات حماية موجودة: {', '.join(found_protection)}")
                else:
                    print("✅ لا توجد علامات حماية واضحة")
                
                # البحث عن محتوى JavaScript
                scripts = soup.find_all('script')
                print(f"📜 عدد scripts: {len(scripts)}")
                
                # البحث عن محتوى أساسي
                main_content = soup.find(['article', 'main', '.entry-content', '.post-content'])
                if main_content:
                    content_text = main_content.get_text()
                    print(f"📋 محتوى أساسي: {len(content_text)} حرف")
                    print(f"📝 بداية المحتوى: {content_text[:200]}...")
                else:
                    print("❌ لا يوجد محتوى أساسي")
                
                # البحث عن عناصر مهمة
                h1_tags = soup.find_all('h1')
                print(f"📌 عدد عناوين H1: {len(h1_tags)}")
                
                if h1_tags:
                    for i, h1 in enumerate(h1_tags[:3]):
                        print(f"  H1[{i}]: {h1.get_text()[:100]}...")
                
                # البحث عن صور
                images = soup.find_all('img')
                print(f"🖼️ عدد الصور: {len(images)}")
                
                # البحث عن روابط
                links = soup.find_all('a')
                print(f"🔗 عدد الروابط: {len(links)}")
                
                # البحث عن نص الصفحة
                page_text = soup.get_text()
                print(f"📄 إجمالي النص: {len(page_text)} حرف")
                
                # البحث عن كلمات مفتاحية
                keywords = ['minecraft', 'mcpe', 'addon', 'mod', 'download']
                found_keywords = []
                
                for keyword in keywords:
                    if keyword in page_text.lower():
                        found_keywords.append(keyword)
                
                print(f"🔍 كلمات مفتاحية موجودة: {', '.join(found_keywords)}")
                
                # حفظ نص مبسط للمراجعة
                text_file = html_file.replace('.html', '_text.txt')
                with open(text_file, 'w', encoding='utf-8') as f:
                    f.write(page_text[:5000])  # أول 5000 حرف
                print(f"💾 تم حفظ النص في: {text_file}")
                
            except Exception as e:
                print(f"❌ خطأ في تحليل {html_file}: {e}")
        else:
            print(f"⚠️ ملف {html_file} غير موجود")

def test_direct_request():
    """اختبار طلب مباشر لفهم الاستجابة"""
    print("\n🌐 اختبار طلب مباشر")
    print("=" * 50)
    
    url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
    
    try:
        # طلب بسيط
        print("📡 طلب بسيط...")
        response = requests.get(url, timeout=10)
        print(f"رمز الاستجابة: {response.status_code}")
        print(f"حجم المحتوى: {len(response.text)} حرف")
        print(f"نوع المحتوى: {response.headers.get('content-type', 'غير محدد')}")
        
        # حفظ الاستجابة
        with open('debug_simple_request.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("💾 تم حفظ الاستجابة في debug_simple_request.html")
        
        # تحليل سريع
        if 'cloudflare' in response.text.lower():
            print("🛡️ تم اكتشاف Cloudflare")
        
        if len(response.text) < 1000:
            print("⚠️ المحتوى قصير جداً")
            print(f"المحتوى: {response.text[:500]}...")
        
    except Exception as e:
        print(f"❌ خطأ في الطلب المباشر: {e}")

def test_cloudscraper_request():
    """اختبار طلب باستخدام cloudscraper"""
    print("\n☁️ اختبار cloudscraper")
    print("=" * 50)
    
    try:
        import cloudscraper
        
        scraper = cloudscraper.create_scraper()
        url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        
        print("📡 طلب باستخدام cloudscraper...")
        response = scraper.get(url, timeout=30)
        print(f"رمز الاستجابة: {response.status_code}")
        print(f"حجم المحتوى: {len(response.text)} حرف")
        
        # حفظ الاستجابة
        with open('debug_cloudscraper_request.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("💾 تم حفظ الاستجابة في debug_cloudscraper_request.html")
        
        # تحليل سريع
        soup = BeautifulSoup(response.text, 'html.parser')
        title = soup.find('title')
        if title:
            print(f"📝 العنوان: {title.get_text()}")
        
        h1_tags = soup.find_all('h1')
        if h1_tags:
            print(f"📌 عناوين H1: {[h1.get_text()[:50] for h1 in h1_tags[:3]]}")
        
    except ImportError:
        print("❌ cloudscraper غير مثبت")
    except Exception as e:
        print(f"❌ خطأ في cloudscraper: {e}")

def check_network_connectivity():
    """فحص الاتصال بالشبكة"""
    print("\n🌍 فحص الاتصال بالشبكة")
    print("=" * 50)
    
    test_urls = [
        "https://www.google.com",
        "https://mcpedl.com",
        "https://httpbin.org/ip"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            print(f"✅ {url}: {response.status_code}")
        except Exception as e:
            print(f"❌ {url}: {e}")

def main():
    """الدالة الرئيسية للتشخيص"""
    print("🔍 تشخيص مشكلة استخراج MCPEDL")
    print("=" * 60)
    
    # فحص الاتصال
    check_network_connectivity()
    
    # تحليل ملفات HTML المحفوظة
    analyze_html_file()
    
    # اختبار طلبات مختلفة
    test_direct_request()
    test_cloudscraper_request()
    
    print("\n" + "=" * 60)
    print("📋 ملخص التشخيص:")
    print("1. راجع ملفات debug_*.html للتفاصيل")
    print("2. راجع ملفات *_text.txt للمحتوى النصي")
    print("3. إذا كان المحتوى قصير، فالموقع يحجب الطلبات")
    print("4. إذا وجدت 'cloudflare'، فهناك حماية إضافية")
    print("5. جرب استخدام Selenium كبديل")
    
    print("\n💡 الحلول المقترحة:")
    print("- python install_selenium_fix.py")
    print("- استخدام VPN")
    print("- المحاولة في وقت آخر")
    print("- الاستخراج اليدوي")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التشخيص")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        input("\nاضغط Enter للخروج...")
