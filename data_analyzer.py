"""
محلل البيانات المستخرجة من مودات mcpedl.com
"""
import json
import logging
from typing import Dict, List, Any, Optional
from collections import Counter
from supabase_client import SupabaseClient

class DataAnalyzer:
    """محلل البيانات المستخرجة"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.supabase_client = SupabaseClient()
    
    def analyze_mods_data(self) -> Dict[str, Any]:
        """تحليل شامل لبيانات المودات"""
        try:
            # جلب جميع المودات
            mods = self.supabase_client.get_all_mods(limit=1000)
            
            if not mods:
                self.logger.warning("لا توجد مودات في قاعدة البيانات")
                return {}
            
            analysis = {
                'total_mods': len(mods),
                'categories': self._analyze_categories(mods),
                'versions': self._analyze_versions(mods),
                'creators': self._analyze_creators(mods),
                'downloads': self._analyze_downloads(mods),
                'images': self._analyze_images(mods),
                'descriptions': self._analyze_descriptions(mods)
            }
            
            self.logger.info(f"تم تحليل {len(mods)} مود")
            return analysis
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل البيانات: {e}")
            return {}
    
    def _analyze_categories(self, mods: List[Dict]) -> Dict[str, Any]:
        """تحليل فئات المودات"""
        categories = [mod.get('category', 'Unknown') for mod in mods if mod.get('category')]
        category_counts = Counter(categories)
        
        return {
            'total_categories': len(category_counts),
            'most_popular': category_counts.most_common(5),
            'distribution': dict(category_counts)
        }
    
    def _analyze_versions(self, mods: List[Dict]) -> Dict[str, Any]:
        """تحليل إصدارات Minecraft المدعومة"""
        all_versions = []
        
        for mod in mods:
            version_str = mod.get('version', '')
            if version_str:
                # استخراج أرقام الإصدارات
                versions = [v.strip() for v in version_str.split(',')]
                all_versions.extend(versions)
        
        version_counts = Counter(all_versions)
        
        return {
            'total_versions': len(version_counts),
            'most_supported': version_counts.most_common(10),
            'distribution': dict(version_counts)
        }
    
    def _analyze_creators(self, mods: List[Dict]) -> Dict[str, Any]:
        """تحليل المطورين"""
        creators = [mod.get('creator_name', 'Unknown') for mod in mods if mod.get('creator_name')]
        creator_counts = Counter(creators)
        
        # تحليل قنوات التواصل الاجتماعي
        social_platforms = []
        for mod in mods:
            social_channels = mod.get('creator_social_channels')
            if social_channels:
                try:
                    if isinstance(social_channels, str):
                        channels = json.loads(social_channels)
                    else:
                        channels = social_channels
                    
                    for channel in channels:
                        if isinstance(channel, dict) and 'platform' in channel:
                            social_platforms.append(channel['platform'])
                except:
                    continue
        
        platform_counts = Counter(social_platforms)
        
        return {
            'total_creators': len(creator_counts),
            'most_active': creator_counts.most_common(10),
            'social_platforms': dict(platform_counts),
            'creators_with_social': len([mod for mod in mods if mod.get('creator_social_channels')])
        }
    
    def _analyze_downloads(self, mods: List[Dict]) -> Dict[str, Any]:
        """تحليل إحصائيات التحميل"""
        downloads = [mod.get('downloads', 0) for mod in mods if mod.get('downloads')]
        likes = [mod.get('likes', 0) for mod in mods if mod.get('likes')]
        
        if not downloads:
            return {'message': 'لا توجد بيانات تحميل متاحة'}
        
        return {
            'total_downloads': sum(downloads),
            'average_downloads': sum(downloads) / len(downloads),
            'max_downloads': max(downloads),
            'min_downloads': min(downloads),
            'total_likes': sum(likes) if likes else 0,
            'average_likes': sum(likes) / len(likes) if likes else 0,
            'mods_with_download_data': len(downloads),
            'mods_with_likes_data': len(likes)
        }
    
    def _analyze_images(self, mods: List[Dict]) -> Dict[str, Any]:
        """تحليل الصور"""
        total_images = 0
        mods_with_images = 0
        
        for mod in mods:
            image_urls = mod.get('image_urls')
            if image_urls:
                try:
                    if isinstance(image_urls, str):
                        images = json.loads(image_urls)
                    else:
                        images = image_urls
                    
                    if isinstance(images, list) and images:
                        total_images += len(images)
                        mods_with_images += 1
                except:
                    continue
        
        return {
            'total_images': total_images,
            'mods_with_images': mods_with_images,
            'average_images_per_mod': total_images / mods_with_images if mods_with_images > 0 else 0,
            'percentage_with_images': (mods_with_images / len(mods)) * 100 if mods else 0
        }
    
    def _analyze_descriptions(self, mods: List[Dict]) -> Dict[str, Any]:
        """تحليل الأوصاف"""
        english_descriptions = [mod.get('description', '') for mod in mods if mod.get('description')]
        arabic_descriptions = [mod.get('description_ar', '') for mod in mods if mod.get('description_ar')]
        
        return {
            'mods_with_english_description': len(english_descriptions),
            'mods_with_arabic_description': len(arabic_descriptions),
            'average_english_length': sum(len(desc) for desc in english_descriptions) / len(english_descriptions) if english_descriptions else 0,
            'average_arabic_length': sum(len(desc) for desc in arabic_descriptions) / len(arabic_descriptions) if arabic_descriptions else 0,
            'percentage_with_english': (len(english_descriptions) / len(mods)) * 100 if mods else 0,
            'percentage_with_arabic': (len(arabic_descriptions) / len(mods)) * 100 if mods else 0
        }
    
    def generate_report(self) -> str:
        """توليد تقرير نصي شامل"""
        analysis = self.analyze_mods_data()
        
        if not analysis:
            return "فشل في توليد التقرير - لا توجد بيانات متاحة"
        
        report = f"""
📊 تقرير تحليل بيانات مودات Minecraft
{'=' * 50}

📈 إحصائيات عامة:
- إجمالي المودات: {analysis['total_mods']}

📂 الفئات:
- عدد الفئات: {analysis['categories']['total_categories']}
- أشهر الفئات: {', '.join([f"{cat} ({count})" for cat, count in analysis['categories']['most_popular'][:3]])}

🎮 الإصدارات:
- عدد الإصدارات المدعومة: {analysis['versions']['total_versions']}
- أكثر الإصدارات دعماً: {', '.join([f"{ver} ({count})" for ver, count in analysis['versions']['most_supported'][:3]])}

👨‍💻 المطورين:
- عدد المطورين: {analysis['creators']['total_creators']}
- المطورين الأكثر نشاطاً: {', '.join([f"{creator} ({count})" for creator, count in analysis['creators']['most_active'][:3]])}
- المطورين مع قنوات تواصل: {analysis['creators']['creators_with_social']}

📥 التحميلات:
- إجمالي التحميلات: {analysis['downloads'].get('total_downloads', 'غير متاح')}
- متوسط التحميلات: {analysis['downloads'].get('average_downloads', 'غير متاح'):.0f}
- أعلى تحميلات: {analysis['downloads'].get('max_downloads', 'غير متاح')}

🖼️ الصور:
- إجمالي الصور: {analysis['images']['total_images']}
- المودات مع صور: {analysis['images']['mods_with_images']} ({analysis['images']['percentage_with_images']:.1f}%)
- متوسط الصور لكل مود: {analysis['images']['average_images_per_mod']:.1f}

📝 الأوصاف:
- مودات مع وصف إنجليزي: {analysis['descriptions']['mods_with_english_description']} ({analysis['descriptions']['percentage_with_english']:.1f}%)
- مودات مع وصف عربي: {analysis['descriptions']['mods_with_arabic_description']} ({analysis['descriptions']['percentage_with_arabic']:.1f}%)
- متوسط طول الوصف الإنجليزي: {analysis['descriptions']['average_english_length']:.0f} حرف
- متوسط طول الوصف العربي: {analysis['descriptions']['average_arabic_length']:.0f} حرف

{'=' * 50}
تم توليد التقرير بنجاح ✅
"""
        
        return report
    
    def export_analysis_to_file(self, filename: str = "mods_analysis.json"):
        """تصدير التحليل إلى ملف JSON"""
        try:
            analysis = self.analyze_mods_data()
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(analysis, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"تم تصدير التحليل إلى {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير التحليل: {e}")
            return False

def main():
    """دالة رئيسية لتشغيل التحليل"""
    analyzer = DataAnalyzer()
    
    # توليد وطباعة التقرير
    report = analyzer.generate_report()
    print(report)
    
    # تصدير التحليل التفصيلي
    analyzer.export_analysis_to_file()

if __name__ == "__main__":
    main()
