#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for the new custom social site features
This script tests the new functionality without running the full GUI
"""

import json
import re

def test_gemini_prompt_parsing():
    """Test parsing of Gemini response with unknown social sites"""
    
    # Sample Gemini response with unknown social sites
    sample_response = """
    {
      "mod_name": "Furniture Mod",
      "version": "1.21.0",
      "full_mod_description": "This mod adds various furniture items to Minecraft Bedrock Edition.",
      "mod_features": ["Chairs", "Tables", "Decorative items"],
      "primary_image_url": "https://example.com/image1.jpg",
      "other_image_urls": ["https://example.com/image2.jpg"],
      "download_link": "https://example.com/download",
      "bp_download_link": null,
      "rp_download_link": null,
      "creator_name": "ModCreator123",
      "creator_contact_info": "<EMAIL>",
      "creator_social_channels": [
        "https://youtube.com/creator123",
        "https://twitter.com/creator123"
      ],
      "unknown_social_sites": [
        {
          "site_name": "Creator Forum",
          "site_url": "https://creator-forum.example.com"
        },
        {
          "site_name": "Telegram Channel",
          "site_url": "https://t.me/creator_channel"
        }
      ]
    }
    """
    
    try:
        data = json.loads(sample_response.strip())
        print("✅ JSON parsing successful")
        
        # Test unknown social sites extraction
        unknown_sites = data.get("unknown_social_sites", [])
        print(f"✅ Found {len(unknown_sites)} unknown social sites")
        
        for i, site in enumerate(unknown_sites):
            site_name = site.get("site_name", "")
            site_url = site.get("site_url", "")
            print(f"   Site {i+1}: {site_name} -> {site_url}")
        
        return True, data
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing failed: {e}")
        return False, None

def test_custom_site_integration():
    """Test integration of custom social sites with existing social channels"""
    
    # Sample data
    creator_social_channels = [
        "https://youtube.com/creator123",
        "https://twitter.com/creator123"
    ]
    
    custom_site_name = "Creator Forum"
    custom_site_url = "https://creator-forum.example.com"
    
    # Test the integration logic
    if custom_site_name and custom_site_url:
        creator_social_channels.append(f"{custom_site_name}: {custom_site_url}")
    
    print("✅ Custom site integration test:")
    print(f"   Original channels: {len(creator_social_channels)-1}")
    print(f"   After adding custom site: {len(creator_social_channels)}")
    print(f"   Final channels: {creator_social_channels}")
    
    return True

def test_database_structure():
    """Test the database structure for new fields"""
    
    # Sample publish data with new fields
    publish_data = {
        "name": "Test Mod",
        "description": "A test mod",
        "category": "Addons",
        "image_urls": ["https://example.com/image.jpg"],
        "version": "1.0.0",
        "size": "2.5 MB",
        "download_url": "https://example.com/download",
        "creator_name": "TestCreator",
        "creator_contact_info": "<EMAIL>",
        "creator_social_channels": [
            "https://youtube.com/testcreator",
            "Creator Forum: https://creator-forum.example.com"
        ],
        "custom_social_site_name": "Creator Forum",
        "custom_social_site_url": "https://creator-forum.example.com"
    }
    
    print("✅ Database structure test:")
    print(f"   All required fields present: {all(key in publish_data for key in ['custom_social_site_name', 'custom_social_site_url'])}")
    print(f"   Custom site name: {publish_data.get('custom_social_site_name')}")
    print(f"   Custom site URL: {publish_data.get('custom_social_site_url')}")
    
    return True

def test_gui_field_update():
    """Test GUI field update logic with unknown social sites"""
    
    # Sample extracted data from Gemini
    extracted_data = {
        "creator_name": "TestCreator",
        "creator_contact_info": "<EMAIL>",
        "creator_social_channels": [
            "https://youtube.com/testcreator",
            "https://twitter.com/testcreator"
        ],
        "unknown_social_sites": [
            {
                "site_name": "Creator Forum",
                "site_url": "https://creator-forum.example.com"
            },
            {
                "site_name": "Telegram Channel", 
                "site_url": "https://t.me/creator_channel"
            }
        ]
    }
    
    # Simulate the GUI update logic
    unknown_social_sites = extracted_data.get("unknown_social_sites", [])
    
    if unknown_social_sites and len(unknown_social_sites) > 0:
        first_unknown_site = unknown_social_sites[0]
        if isinstance(first_unknown_site, dict):
            site_name = first_unknown_site.get("site_name", "")
            site_url = first_unknown_site.get("site_url", "")
            
            print("✅ GUI field update test:")
            print(f"   First unknown site: {site_name} -> {site_url}")
            
            # Test multiple unknown sites
            if len(unknown_social_sites) > 1:
                additional_sites = []
                for site in unknown_social_sites[1:]:
                    if isinstance(site, dict):
                        name = site.get("site_name", "")
                        url = site.get("site_url", "")
                        if name and url:
                            additional_sites.append(f"{name}: {url}")
                
                print(f"   Additional sites for social channels: {additional_sites}")
    
    return True

def run_all_tests():
    """Run all tests"""
    print("🧪 Testing new custom social site features...\n")
    
    tests = [
        ("Gemini Prompt Parsing", test_gemini_prompt_parsing),
        ("Custom Site Integration", test_custom_site_integration),
        ("Database Structure", test_database_structure),
        ("GUI Field Update", test_gui_field_update)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🔍 Running {test_name}...")
        try:
            if test_name == "Gemini Prompt Parsing":
                success, data = test_func()
            else:
                success = test_func()
            
            if success:
                print(f"✅ {test_name} passed\n")
                passed += 1
            else:
                print(f"❌ {test_name} failed\n")
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}\n")
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The new features are working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    run_all_tests()
