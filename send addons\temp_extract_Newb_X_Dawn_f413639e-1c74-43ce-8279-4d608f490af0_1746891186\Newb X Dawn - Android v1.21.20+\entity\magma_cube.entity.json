{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:magma_cube", "materials": {"default": "entity_emissive_alpha"}, "textures": {"default": "textures/entity/slime/magmacube"}, "geometry": {"default": "geometry.lavaslime"}, "scripts": {"pre_animation": ["variable.slime_squish_factor = (query.previous_squish_value + (query.current_squish_value - query.previous_squish_value) * query.frame_alpha);", "variable.bounce = 1 / ((variable.slime_squish_factor / (query.variant * 0.5 + 1)) + 1);", "variable.horizontal_scale_amount = variable.bounce * query.variant;", "variable.vertical_scale_amount = (1 / variable.bounce) * query.variant;"], "scaleX": "variable.horizontal_scale_amount", "scaleY": "variable.vertical_scale_amount", "scaleZ": "variable.horizontal_scale_amount"}, "animations": {"move": "animation.magma_cube.move"}, "animation_controllers": [{"general": "controller.animation.magma_cube.general"}], "render_controllers": ["controller.render.magma_cube"], "spawn_egg": {"texture": "spawn_egg", "texture_index": 20}}}}