{
  "format_version": "1.8.0",
  "minecraft:client_entity": {
    "description": {
      "identifier": "minecraft:zombie_villager_v2",
      "materials": {
        "default": "spider",
        "masked": "zombie_villager_v2_masked"
      },
      "textures": {
        "default": "textures/entity/zombie_villager2/zombie-villager",

        // Biome Layer
        "desert": "textures/entity/zombie_villager2/biomes/biome-desert-zombie",
        "jungle": "textures/entity/zombie_villager2/biomes/biome-jungle-zombie",
        "plains": "textures/entity/zombie_villager2/biomes/biome-plains-zombie",
        "savanna": "textures/entity/zombie_villager2/biomes/biome-savanna-zombie",
        "snow": "textures/entity/zombie_villager2/biomes/biome-snow-zombie",
        "swamp": "textures/entity/zombie_villager2/biomes/biome-swamp-zombie",
        "taiga": "textures/entity/zombie_villager2/biomes/biome-taiga-zombie",

        // Profession Layer
        "armorer": "textures/entity/zombie_villager2/professions/armorer",
        "butcher": "textures/entity/zombie_villager2/professions/butcher",
        "cartographer": "textures/entity/zombie_villager2/professions/cartographer",
        "cleric": "textures/entity/zombie_villager2/professions/cleric",
        "farmer": "textures/entity/zombie_villager2/professions/farmer",
        "fisherman": "textures/entity/zombie_villager2/professions/fisherman",
        "fletcher": "textures/entity/zombie_villager2/professions/fletcher",
        "leatherworker": "textures/entity/zombie_villager2/professions/leatherworker",
        "librarian": "textures/entity/zombie_villager2/professions/librarian",
        "shepherd": "textures/entity/zombie_villager2/professions/shepherd",
        "tool_smith": "textures/entity/zombie_villager2/professions/toolsmith",
        "weapon_smith": "textures/entity/zombie_villager2/professions/weaponsmith",
        "stonemason": "textures/entity/zombie_villager2/professions/stonemason",
        "nitwit": "textures/entity/zombie_villager2/professions/nitwit",
        "unskilled": "textures/entity/zombie_villager2/professions/unskilled"
      },
      "geometry": {
        "default": "geometry.zombie.villager_v2"
      },
      "scripts": {
        "pre_animation": [
          "variable.num_professions = 15;",
          "variable.profession_index = ((query.variant < variable.num_professions) ? query.variant : 0);",
          "variable.tcos0 = (Math.cos(query.modified_distance_moved * 38.17) * query.modified_move_speed / variable.gliding_speed_value) * 57.3;"
        ]
      },
      "animations": {
        "humanoid_big_head": "animation.humanoid.big_head",
        "humanoid_base_pose": "animation.humanoid.base_pose",
        "look_at_target_default": "animation.humanoid.look_at_target.default",
        "look_at_target_gliding": "animation.humanoid.look_at_target.gliding",
        "look_at_target_swimming": "animation.humanoid.look_at_target.swimming",
        "move": "animation.humanoid.move",
        "riding.arms": "animation.humanoid.riding.arms",
        "riding.legs": "animation.humanoid.riding.legs",
        "holding": "animation.humanoid.holding",
        "brandish_spear": "animation.humanoid.brandish_spear",
        "charging": "animation.humanoid.charging",
        "attack.rotations": "animation.humanoid.attack.rotations",
        "sneaking": "animation.humanoid.sneaking",
        "bob": "animation.humanoid.bob",
        "damage_nearby_mobs": "animation.humanoid.damage_nearby_mobs",
        "bow_and_arrow": "animation.humanoid.bow_and_arrow",
        "use_item_progress": "animation.humanoid.use_item_progress",
        "zombie_attack_bare_hand": "animation.zombie.attack_bare_hand",
        "swimming": "animation.zombie.swimming"
      },
      "animation_controllers": [
        { "humanoid_baby_big_head": "controller.animation.humanoid.baby_big_head" },
        { "humanoid_base_pose": "controller.animation.humanoid.base_pose" },
        { "look_at_target": "controller.animation.humanoid.look_at_target" },
        { "move": "controller.animation.humanoid.move" },
        { "riding": "controller.animation.humanoid.riding" },
        { "holding": "controller.animation.humanoid.holding" },
        { "brandish_spear": "controller.animation.humanoid.brandish_spear" },
        { "charging": "controller.animation.humanoid.charging" },
        { "attack": "controller.animation.humanoid.attack" },
        { "sneaking": "controller.animation.humanoid.sneaking" },
        { "bob": "controller.animation.humanoid.bob" },
        { "damage_nearby_mobs": "controller.animation.humanoid.damage_nearby_mobs" },
        { "bow_and_arrow": "controller.animation.humanoid.bow_and_arrow" },
        { "use_item_progress": "controller.animation.humanoid.use_item_progress" },
        { "zombie_attack_bare_hand": "controller.animation.zombie.attack_bare_hand" },
        { "swimming": "controller.animation.zombie.swimming" }
      ],
      "render_controllers": [
        "controller.render.zombie_villager_v2_base",
        "controller.render.zombie_villager_v2_masked"
      ],
      "enable_attachables": true,
      "spawn_egg": {
        "texture": "spawn_egg",
        "texture_index": 42
      }
    }
  }
}