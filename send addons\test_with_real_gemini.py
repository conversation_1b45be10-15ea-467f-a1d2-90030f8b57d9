# -*- coding: utf-8 -*-
"""
اختبار نهائي مع Gemini API الحقيقي
"""

import sys
import os

# إضافة المجلد الحالي إلى المسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_with_real_gemini():
    """اختبار مع Gemini API الحقيقي"""
    print("🤖 اختبار مع Gemini API الحقيقي...")
    
    try:
        from gemini_description_generator import GeminiDescriptionGenerator
        
        # استخدام المفتاح مباشرة
        api_key = "AIzaSyDZfiLXBVs8yBk0CDb4hvLZ_l8P6tKy6og"
        
        generator = GeminiDescriptionGenerator(api_key)
        
        if not generator.model:
            print("❌ فشل في تهيئة Gemini")
            return False
        
        # بيانات تجريبية لـ ezRTX
        test_mod_data = {
            'name': 'ezRTX',
            'category': 'Texture Pack',
            'creator_name': 'tofeelez',
            'version': 'Android',
            'size': '7.1 MB'
        }
        
        test_content = """
        ezRTX Vanilla-style ray tracing resource pack. There is no RTX pack on the internet that is as detailed, realistically tuned, balanced, and still retains the vanilla feel. All textures have not been subjected to changes in appearance, color, saturation with the exception of weather and particles for a better sense of realism compatible with vanilla. Maximized balanced glow, metallic and smoothness effects to achieve the best comfort experience without RTX graphics artifacts. No unnecessary luminous elements and blocks, only what should be luminous is luminous. The luminous blocks have a proper vanilla hierarchy and are set up so that blocks with the same light level glow the same way. Every aspect was taken into account to logically identify the objects. Also included is an improved settings panel with an integrated V-Sync switch and fixes for all known bugs. For an enhanced experience, ezRTX has been adapted to BetterRTX, which improves RTX visuals and allows you to activate glowing entities: Ender Chest, End Crystal, Enchanting Table Book, Experience Orb, and mobs.
        """
        
        print("🔄 إنشاء أوصاف بواسطة Gemini...")
        
        english_desc, arabic_desc = generator.generate_descriptions(test_mod_data, test_content)
        
        print(f"\n📝 الوصف الإنجليزي ({len(english_desc)} حرف):")
        print(f"{english_desc}")
        
        print(f"\n📝 الوصف العربي ({len(arabic_desc)} حرف):")
        print(f"{arabic_desc}")
        
        # فحص الجودة
        if len(english_desc) > 300 and len(arabic_desc) > 300:
            print("\n✅ تم إنشاء أوصاف مفصلة بواسطة Gemini!")
            return True
        else:
            print("\n⚠️ الأوصاف قصيرة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_full_extraction_with_gemini():
    """اختبار الاستخراج الكامل مع Gemini"""
    print("\n🔍 اختبار الاستخراج الكامل مع Gemini...")
    
    try:
        # تعديل مؤقت لملف المستخرج لاستخدام المفتاح مباشرة
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        
        # إنشاء مستخرج مخصص
        class CustomExtractor(MCPEDLExtractorFixed):
            def get_gemini_api_key(self):
                return "AIzaSyDZfiLXBVs8yBk0CDb4hvLZ_l8P6tKy6og"
        
        # اختبار مع HTML محفوظ
        if os.path.exists('debug_specific_url.html'):
            with open('debug_specific_url.html', 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            extractor = CustomExtractor()
            test_url = "https://mcpedl.com/ezrtx/"
            
            result = extractor.extract_mod_data(html_content, test_url, generate_ai_descriptions=True)
            
            if result:
                print(f"✅ نجح الاستخراج مع Gemini!")
                print(f"📊 النتائج:")
                print(f"   - الاسم: {result['name']}")
                print(f"   - عدد الصور: {len(result['image_urls'])}")
                print(f"   - طول الوصف الإنجليزي: {len(result['description'])} حرف")
                print(f"   - طول الوصف العربي: {len(result['description_arabic'])} حرف")
                
                # عرض جزء من الأوصاف
                print(f"\n📝 جزء من الوصف الإنجليزي:")
                print(f"{result['description'][:200]}...")
                
                print(f"\n📝 جزء من الوصف العربي:")
                print(f"{result['description_arabic'][:200]}...")
                
                return True
            else:
                print("❌ فشل الاستخراج")
                return False
        else:
            print("⚠️ ملف HTML غير موجود")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نهائي مع Gemini API الحقيقي")
    print("=" * 60)
    
    results = {}
    
    # اختبار Gemini مباشرة
    results['direct_gemini'] = test_with_real_gemini()
    
    # اختبار الاستخراج الكامل
    results['full_extraction'] = test_full_extraction_with_gemini()
    
    # تلخيص النتائج
    print("\n📊 ملخص النتائج:")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name}: {status}")
    
    success_rate = sum(results.values()) / len(results)
    print(f"\nمعدل النجاح: {success_rate*100:.1f}%")
    
    if success_rate >= 0.8:
        print("\n🎉 Gemini يعمل بشكل مثالي!")
        print("✅ أوصاف مفصلة وذكية")
        print("✅ تكامل كامل مع المستخرج")
    else:
        print("\n⚠️ هناك مشاكل في Gemini")

if __name__ == "__main__":
    main()
