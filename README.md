# أداة استخراج بيانات مودات Minecraft من mcpedl.com

أداة Python متقدمة لاستخراج بيانات مودات Minecraft من موقع mcpedl.com وتخزينها في قاعدة بيانات Supabase مع توليد أوصاف باللغتين العربية والإنجليزية.

## الميزات الرئيسية

- 🔍 **استخراج شامل للبيانات**: اسم المود، الوصف، الفئة، الصور، الإصدارات المدعومة، معلومات المطور
- 🤖 **توليد أوصاف ذكية**: استخدام نماذج اللغة الكبيرة لتوليد أوصاف جذابة باللغتين العربية والإنجليزية
- 💾 **تكامل مع Supabase**: حفظ البيانات تلقائياً في قاعدة بيانات Supabase
- 🛡️ **معالجة الأخطاء**: نظام قوي لمعالجة الأخطاء والاستثناءات
- 📊 **تسجيل مفصل**: نظام تسجيل شامل لمتابعة العمليات
- ⚡ **معالجة متعددة**: دعم معالجة عدة روابط دفعة واحدة

## متطلبات النظام

- Python 3.8 أو أحدث
- اتصال بالإنترنت
- حساب Supabase (مجاني)
- مفتاح OpenAI API (اختياري)

## التثبيت

1. **استنساخ المشروع:**
```bash
git clone <repository-url>
cd mcpedl-scraper
```

2. **تثبيت المكتبات المطلوبة:**
```bash
pip install -r requirements.txt
```

3. **إعداد متغيرات البيئة:**
```bash
cp .env.example .env
```

4. **تحرير ملف .env وإضافة المفاتيح:**
```env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your-service-key-here
OPENAI_API_KEY=your-openai-api-key-here  # اختياري
```

## إعداد قاعدة البيانات

قم بإنشاء جدول `mods` في Supabase بالهيكل التالي:

```sql
CREATE TABLE mods (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    description_ar TEXT,
    category TEXT,
    image_urls JSONB,
    version TEXT,
    size TEXT,
    download_url TEXT,
    source_url TEXT UNIQUE,
    downloads INTEGER DEFAULT 0,
    likes INTEGER DEFAULT 0,
    creator_name TEXT,
    creator_contact_info TEXT,
    creator_social_channels JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## الاستخدام

### استخراج مود واحد

```bash
python main.py --url "https://mcpedl.com/dragon-mounts-v1-3-25/"
```

### استخراج عدة مودات من ملف

1. **إنشاء ملف نصي** (مثل `urls.txt`) يحتوي على روابط المودات:
```
https://mcpedl.com/dragon-mounts-v1-3-25/
https://mcpedl.com/another-mod/
https://mcpedl.com/third-mod/
```

2. **تشغيل الأداة:**
```bash
python main.py --file urls.txt
```

### خيارات إضافية

```bash
# عدم تخطي المودات الموجودة (تحديث البيانات)
python main.py --url "https://mcpedl.com/mod-name/" --no-skip-existing

# عرض تفاصيل أكثر في السجلات
python main.py --url "https://mcpedl.com/mod-name/" --verbose

# عرض المساعدة
python main.py --help
```

## هيكل المشروع

```
mcpedl-scraper/
├── main.py                 # الملف الرئيسي
├── config.py              # إعدادات التطبيق
├── scraper.py             # كلاس استخراج البيانات
├── supabase_client.py     # عميل قاعدة البيانات
├── llm_generator.py       # مولد الأوصاف
├── utils.py               # دوال مساعدة
├── requirements.txt       # المكتبات المطلوبة
├── .env.example          # مثال متغيرات البيئة
└── README.md             # هذا الملف
```

## البيانات المستخرجة

تستخرج الأداة البيانات التالية لكل مود:

- **name**: اسم المود
- **description**: وصف مولد باللغة الإنجليزية
- **description_ar**: وصف مولد باللغة العربية
- **category**: فئة المود
- **image_urls**: روابط الصور (JSON)
- **version**: الإصدارات المدعومة
- **size**: حجم الملف
- **download_url**: رابط التحميل
- **source_url**: رابط الصفحة الأصلية
- **downloads**: عدد التحميلات
- **likes**: عدد الإعجابات
- **creator_name**: اسم المطور
- **creator_contact_info**: معلومات الاتصال
- **creator_social_channels**: قنوات التواصل الاجتماعي (JSON)

## معالجة الأخطاء

- **روابط غير صحيحة**: يتم التحقق من صحة الروابط قبل المعالجة
- **صفحات غير موجودة**: معالجة أخطاء 404 وأخطاء الشبكة
- **تغيير هيكل الموقع**: نظام محددات CSS مرن قابل للتحديث
- **حماية ضد الروبوتات**: ترويسات HTTP واقعية وتأخير بين الطلبات

## التخصيص

### تحديث محددات CSS

يمكن تحديث محددات CSS في ملف `config.py` إذا تغير هيكل موقع mcpedl.com:

```python
SELECTORS = {
    'title': 'h1.entry-title, h1.post-title, .post-header h1',
    'description': '.entry-content p, .post-content p, .content p',
    # ... باقي المحددات
}
```

### إضافة نماذج لغوية أخرى

يمكن تعديل ملف `llm_generator.py` لدعم نماذج لغوية أخرى مثل Claude أو Gemini.

## المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## التشغيل السريع

للبدء السريع والتحقق من أن كل شيء يعمل بشكل صحيح:

```bash
python quick_start.py
```

هذا السكربت سيقوم بـ:
- التحقق من المتطلبات
- التحقق من متغيرات البيئة
- اختبار الوظائف الأساسية
- تشغيل استخراج تجريبي (اختياري)

## واجهة سطر الأوامر المتقدمة

للمزيد من الخيارات المتقدمة، استخدم `cli_manager.py`:

```bash
# استخراج البيانات
python cli_manager.py extract --url "https://mcpedl.com/mod-name/"
python cli_manager.py extract --file urls.txt

# تحليل البيانات
python cli_manager.py analyze --report
python cli_manager.py analyze --export analysis.json

# إدارة قاعدة البيانات
python cli_manager.py db --list --limit 10
python cli_manager.py db --search "dragon"

# أدوات مساعدة
python cli_manager.py utils --validate-urls urls.txt
python cli_manager.py utils --check-config
```

## الاختبار

لاختبار الأداة:

```bash
python test_scraper.py
```

## تحليل البيانات

لتحليل البيانات المخزنة:

```bash
python data_analyzer.py
```

## الملفات المهمة

- `main.py` - الملف الرئيسي للاستخراج البسيط
- `cli_manager.py` - واجهة سطر الأوامر المتقدمة
- `quick_start.py` - التشغيل السريع والاختبار
- `test_scraper.py` - اختبار الأداة
- `data_analyzer.py` - تحليل البيانات
- `database_setup.sql` - إعداد قاعدة البيانات

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **خطأ في الاتصال بـ Supabase:**
   - تأكد من صحة `SUPABASE_URL` و `SUPABASE_SERVICE_KEY`
   - تأكد من تشغيل قاعدة البيانات

2. **فشل في استخراج البيانات:**
   - تحقق من صحة الرابط
   - تأكد من الاتصال بالإنترنت
   - قد يكون الموقع قد غير هيكله

3. **خطأ في توليد الأوصاف:**
   - تحقق من صحة `OPENAI_API_KEY`
   - الأداة ستعمل بدون OpenAI لكن بأوصاف احتياطية

4. **مشاكل في المكتبات:**
   ```bash
   pip install --upgrade -r requirements.txt
   ```

## المساهمة

نرحب بالمساهمات! خطوات المساهمة:

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## إخلاء المسؤولية

هذه الأداة مخصصة للأغراض التعليمية والبحثية. يرجى احترام شروط استخدام موقع mcpedl.com وعدم إساءة استخدام الأداة.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات:

1. تحقق من قسم استكشاف الأخطاء أعلاه
2. شغل `python quick_start.py` للتشخيص
3. إنشاء Issue في المستودع مع تفاصيل المشكلة

## الإصدارات المستقبلية

الميزات المخطط لها:
- دعم مواقع مودات أخرى
- واجهة ويب للإدارة
- تحسينات في دقة الاستخراج
- دعم المزيد من نماذج اللغة
- إحصائيات متقدمة أكثر
