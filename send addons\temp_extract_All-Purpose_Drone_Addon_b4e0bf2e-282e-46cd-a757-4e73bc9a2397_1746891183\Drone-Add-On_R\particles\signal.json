{"format_version": "1.10.0", "particle_effect": {"description": {"identifier": "drone_emitter", "basic_render_parameters": {"material": "particles_blend", "texture": "textures/particles/signal"}}, "components": {"minecraft:emitter_rate_instant": {"num_particles": 1}, "minecraft:emitter_lifetime_once": {"active_time": 1}, "minecraft:emitter_shape_point": {"direction": ["v.direction.direction_x", "v.direction.direction_y", "v.direction.direction_z"]}, "minecraft:particle_lifetime_expression": {"max_lifetime": "Math.random(5.7, 6.5)"}, "minecraft:particle_initial_speed": "v.direction.speed", "minecraft:particle_motion_dynamic": {}, "minecraft:particle_appearance_billboard": {"size": [0.07, 0.07], "facing_camera_mode": "lookat_xyz", "uv": {"texture_width": 64, "texture_height": 8, "flipbook": {"base_UV": [56, 0], "size_UV": [8, 8], "step_UV": [-8, 0], "frames_per_second": 4, "max_frame": 8, "stretch_to_lifetime": true}}}, "minecraft:particle_motion_collision": {"collision_radius": 0.1, "expire_on_contact": true}, "minecraft:particle_appearance_lighting": {}, "minecraft:particle_appearance_tinting": {"color": {"interpolant": "v.particle_age / v.particle_lifetime", "gradient": {"0.0": "#52FFFFFF", "1.0": "#C4FFFFFF"}}}}}}