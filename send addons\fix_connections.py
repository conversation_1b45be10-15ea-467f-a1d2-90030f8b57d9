# -*- coding: utf-8 -*-
"""
أداة إصلاح مشاكل الاتصال بـ Supabase و Gemini
"""

import os
import sys
import json
import requests
import time

def test_internet_connection():
    """اختبار الاتصال بالإنترنت"""
    print("🌐 اختبار الاتصال بالإنترنت...")
    
    try:
        response = requests.get("https://www.google.com", timeout=10)
        if response.status_code == 200:
            print("✅ الاتصال بالإنترنت يعمل")
            return True
        else:
            print(f"⚠️ مشكلة في الاتصال: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ فشل الاتصال بالإنترنت: {e}")
        return False

def test_supabase_storage():
    """اختبار الاتصال بـ Supabase Storage"""
    print("\n📦 اختبار الاتصال بـ Supabase Storage...")
    
    STORAGE_URL = "https://mwxzwfeqsashcwvqthmd.supabase.co"
    STORAGE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im13eHp3ZmVxc2FzaGN3dnF0aG1kIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4MzU2NDcsImV4cCI6MjA2MTQxMTY0N30.nU0smAgNsoLi1zRNKA3AFM3q112jp4fhPgYeeXqKmPU"
    
    try:
        from supabase import create_client
        
        storage_client = create_client(STORAGE_URL, STORAGE_KEY)
        
        # اختبار قائمة buckets
        buckets = storage_client.storage.list_buckets()
        print(f"✅ تم الاتصال بـ Supabase Storage بنجاح")
        print(f"📁 عدد buckets: {len(buckets)}")
        return True
        
    except ImportError:
        print("❌ مكتبة supabase غير مثبتة")
        print("💡 قم بتثبيتها: pip install supabase")
        return False
    except Exception as e:
        print(f"❌ فشل الاتصال بـ Supabase Storage: {e}")
        
        # اختبار الاتصال المباشر
        try:
            response = requests.get(f"{STORAGE_URL}/rest/v1/", timeout=10)
            print(f"🔍 اختبار مباشر - رمز الاستجابة: {response.status_code}")
        except Exception as direct_e:
            print(f"🔍 فشل الاختبار المباشر: {direct_e}")
        
        return False

def test_supabase_database():
    """اختبار الاتصال بـ Supabase Database"""
    print("\n🗄️ اختبار الاتصال بـ Supabase Database...")
    
    APP_DB_URL = 'https://ytqxxodyecdeosnqoure.supabase.co'
    APP_DB_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExMDUsImV4cCI6MjA2MDgzNzEwNX0.d4gEtkXNCeFB1rjK0Qnih8cdjKRSilljr5aSgJ0ooQ4'
    
    try:
        from supabase import create_client
        
        app_db_client = create_client(APP_DB_URL, APP_DB_KEY)
        
        # اختبار الوصول لجدول mods
        result = app_db_client.table('mods').select("id").limit(1).execute()
        print(f"✅ تم الاتصال بـ Supabase Database بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ فشل الاتصال بـ Supabase Database: {e}")
        
        # اختبار الاتصال المباشر
        try:
            response = requests.get(f"{APP_DB_URL}/rest/v1/", timeout=10)
            print(f"🔍 اختبار مباشر - رمز الاستجابة: {response.status_code}")
        except Exception as direct_e:
            print(f"🔍 فشل الاختبار المباشر: {direct_e}")
        
        return False

def test_gemini_api():
    """اختبار Gemini API"""
    print("\n🤖 اختبار Gemini API...")
    
    # البحث عن مفتاح API
    api_key = None
    
    # البحث في ملف config.json
    config_files = ['config.json', '../config.json']
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                if 'gemini_api_keys' in config and config['gemini_api_keys']:
                    api_key = config['gemini_api_keys'][0]
                    break
                elif 'gemini_api_key' in config and config['gemini_api_key']:
                    api_key = config['gemini_api_key']
                    break
            except Exception as e:
                print(f"خطأ في قراءة {config_file}: {e}")
    
    if not api_key:
        print("⚠️ لم يتم العثور على مفتاح Gemini API")
        return False
    
    print(f"🔑 تم العثور على مفتاح API: {api_key[:10]}...{api_key[-5:]}")
    
    try:
        import google.generativeai as genai
        
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-pro')
        
        response = model.generate_content("Say hello in one word")
        
        if response and response.text:
            print(f"✅ Gemini API يعمل: {response.text.strip()}")
            return True
        else:
            print("❌ Gemini API لا يستجيب")
            return False
            
    except ImportError:
        print("❌ مكتبة google-generativeai غير مثبتة")
        print("💡 قم بتثبيتها: pip install google-generativeai")
        return False
    except Exception as e:
        print(f"❌ خطأ في Gemini API: {e}")
        return False

def create_fixed_config():
    """إنشاء ملف config محسن"""
    print("\n🔧 إنشاء ملف config محسن...")
    
    config_data = {
        "gemini_api_keys": [
            "AIzaSyDZfiLXBVs8yBk0CDb4hvLZ_l8P6tKy6og"
        ],
        "gemini_api_key": "AIzaSyDZfiLXBVs8yBk0CDb4hvLZ_l8P6tKy6og",
        "ai_descriptions_enabled": True,
        "description_language": "both",
        "max_description_length": 800,
        "fallback_descriptions": True
    }
    
    try:
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        print("✅ تم إنشاء ملف config.json محسن")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف config: {e}")
        return False

def provide_solutions():
    """تقديم حلول للمشاكل"""
    print("\n💡 الحلول المقترحة:")
    print("=" * 50)
    
    print("🌐 مشاكل الاتصال بالإنترنت:")
    print("   • تحقق من اتصال الإنترنت")
    print("   • جرب VPN إذا كان هناك حجب")
    print("   • تأكد من إعدادات Firewall")
    
    print("\n📦 مشاكل Supabase Storage:")
    print("   • تحقق من صحة STORAGE_URL و STORAGE_KEY")
    print("   • تأكد من أن المشروع نشط في Supabase")
    print("   • تحقق من صلاحيات API Key")
    
    print("\n🗄️ مشاكل Supabase Database:")
    print("   • تحقق من صحة APP_DB_URL و APP_DB_KEY")
    print("   • تأكد من وجود جدول 'mods'")
    print("   • تحقق من صلاحيات قراءة/كتابة الجدول")
    
    print("\n🤖 مشاكل Gemini API:")
    print("   • تأكد من صحة مفتاح API")
    print("   • تحقق من حصة API المتبقية")
    print("   • تثبيت المكتبة: pip install google-generativeai")
    
    print("\n🔧 خطوات الإصلاح:")
    print("   1. تشغيل: pip install supabase google-generativeai")
    print("   2. إعادة تشغيل الأداة")
    print("   3. إذا استمرت المشاكل، استخدم الأداة بدون Supabase")

def run_offline_mode_test():
    """اختبار الوضع المحلي بدون Supabase"""
    print("\n🔄 اختبار الوضع المحلي...")
    
    try:
        # اختبار MCPEDL scraper فقط
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from mcpedl_scraper_module import scrape_mcpedl_mod
        
        test_url = "https://mcpedl.com/ezrtx/"
        print(f"🔗 اختبار استخراج من: {test_url}")
        
        result = scrape_mcpedl_mod(test_url)
        
        if result:
            print("✅ استخراج البيانات يعمل بدون Supabase!")
            print(f"📊 النتائج:")
            print(f"   - الاسم: {result.get('name', 'غير متوفر')}")
            print(f"   - الفئة: {result.get('category', 'غير متوفر')}")
            print(f"   - عدد الصور: {len(result.get('image_urls', []))}")
            print(f"   - طول الوصف: {len(result.get('description', ''))} حرف")
            return True
        else:
            print("❌ فشل في استخراج البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار المحلي: {e}")
        return False

def main():
    """الدالة الرئيسية للتشخيص"""
    print("🔧 أداة تشخيص وإصلاح مشاكل الاتصال")
    print("=" * 60)
    
    results = {}
    
    # اختبار الاتصالات
    results['internet'] = test_internet_connection()
    results['supabase_storage'] = test_supabase_storage()
    results['supabase_database'] = test_supabase_database()
    results['gemini_api'] = test_gemini_api()
    
    # إنشاء config محسن
    results['config_creation'] = create_fixed_config()
    
    # اختبار الوضع المحلي
    results['offline_mode'] = run_offline_mode_test()
    
    # تلخيص النتائج
    print("\n📊 ملخص النتائج:")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ يعمل" if result else "❌ لا يعمل"
        print(f"{test_name}: {status}")
    
    # تقييم عام
    working_count = sum(results.values())
    total_count = len(results)
    
    print(f"\nالحالة العامة: {working_count}/{total_count} يعمل")
    
    if results['offline_mode']:
        print("\n🎉 الأداة تعمل في الوضع المحلي!")
        print("💡 يمكنك استخدام استخراج البيانات بدون Supabase")
        print("📝 البيانات ستُحفظ محلياً ويمكن نسخها يدوياً")
    
    # تقديم الحلول
    provide_solutions()
    
    print(f"\n🚀 التوصية:")
    if working_count >= 4:
        print("جميع الخدمات تعمل - الأداة جاهزة!")
    elif results['offline_mode']:
        print("استخدم الأداة في الوضع المحلي")
    else:
        print("راجع الحلول المقترحة أعلاه")

if __name__ == "__main__":
    main()
