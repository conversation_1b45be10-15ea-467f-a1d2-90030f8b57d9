"""
مولد الأوصاف باستخدام نماذج اللغة الكبيرة
"""
import logging
from typing import Dict, Optional, Tuple
import openai

from config import Config

class LLMDescriptionGenerator:
    """مولد الأوصاف باستخدام OpenAI GPT"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """تهيئة عميل OpenAI"""
        try:
            if Config.OPENAI_API_KEY:
                openai.api_key = Config.OPENAI_API_KEY
                self.client = openai
                self.logger.info("تم تهيئة عميل OpenAI بنجاح")
            else:
                self.logger.warning("مفتاح OpenAI API غير متوفر - سيتم تخطي توليد الأوصاف")
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة عميل OpenAI: {e}")
    
    def _create_prompt(self, mod_data: Dict) -> str:
        """إنشاء prompt لتوليد الوصف"""
        name = mod_data.get('name', 'Unknown Mod')
        description = mod_data.get('description', '')
        category = mod_data.get('category', 'Mods')
        version = mod_data.get('version', '')
        
        prompt = f"""
أنت خبير في مودات Minecraft وكاتب محتوى محترف. مهمتك هي إنشاء وصفين جذابين ومفصلين لمود Minecraft.

معلومات المود:
- الاسم: {name}
- الفئة: {category}
- الإصدارات المدعومة: {version}
- الوصف الأصلي: {description}

المطلوب:
1. وصف باللغة الإنجليزية (150-300 كلمة)
2. وصف باللغة العربية (150-300 كلمة)

متطلبات الوصف:
- جذاب ومثير للاهتمام
- يركز على الميزات الرئيسية
- يوضح فوائد المود للاعبين
- يتضمن كلمات مفتاحية مناسبة
- مناسب للنشر على المواقع والمتاجر

تنسيق الإجابة:
ENGLISH_DESCRIPTION:
[الوصف الإنجليزي هنا]

ARABIC_DESCRIPTION:
[الوصف العربي هنا]
"""
        return prompt
    
    def generate_descriptions(self, mod_data: Dict) -> Tuple[Optional[str], Optional[str]]:
        """توليد الأوصاف باللغتين الإنجليزية والعربية"""
        if not self.client:
            self.logger.warning("عميل OpenAI غير متوفر")
            return None, None
        
        try:
            prompt = self._create_prompt(mod_data)
            
            self.logger.info(f"توليد أوصاف للمود: {mod_data.get('name', 'Unknown')}")
            
            response = self.client.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system",
                        "content": "أنت خبير في مودات Minecraft وكاتب محتوى محترف متخصص في إنشاء أوصاف جذابة ومفصلة."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=800,
                temperature=0.7
            )
            
            content = response.choices[0].message.content
            
            # استخراج الأوصاف من الاستجابة
            english_desc, arabic_desc = self._parse_response(content)
            
            if english_desc and arabic_desc:
                self.logger.info("تم توليد الأوصاف بنجاح")
                return english_desc, arabic_desc
            else:
                self.logger.warning("فشل في استخراج الأوصاف من الاستجابة")
                return None, None
                
        except Exception as e:
            self.logger.error(f"خطأ في توليد الأوصاف: {e}")
            return None, None
    
    def _parse_response(self, content: str) -> Tuple[Optional[str], Optional[str]]:
        """استخراج الأوصاف من استجابة النموذج"""
        try:
            english_desc = None
            arabic_desc = None
            
            # البحث عن الوصف الإنجليزي
            if "ENGLISH_DESCRIPTION:" in content:
                start = content.find("ENGLISH_DESCRIPTION:") + len("ENGLISH_DESCRIPTION:")
                end = content.find("ARABIC_DESCRIPTION:")
                if end == -1:
                    end = len(content)
                english_desc = content[start:end].strip()
            
            # البحث عن الوصف العربي
            if "ARABIC_DESCRIPTION:" in content:
                start = content.find("ARABIC_DESCRIPTION:") + len("ARABIC_DESCRIPTION:")
                arabic_desc = content[start:].strip()
            
            # تنظيف الأوصاف
            if english_desc:
                english_desc = self._clean_description(english_desc)
            if arabic_desc:
                arabic_desc = self._clean_description(arabic_desc)
            
            return english_desc, arabic_desc
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل استجابة النموذج: {e}")
            return None, None
    
    def _clean_description(self, description: str) -> str:
        """تنظيف الوصف من الأحرف غير المرغوب فيها"""
        if not description:
            return ""
        
        # إزالة الأسطر الفارغة الزائدة
        lines = [line.strip() for line in description.split('\n') if line.strip()]
        cleaned = '\n'.join(lines)
        
        # إزالة الأحرف الخاصة في البداية والنهاية
        cleaned = cleaned.strip('*-_=+[]{}()')
        
        return cleaned.strip()
    
    def generate_fallback_descriptions(self, mod_data: Dict) -> Tuple[str, str]:
        """توليد أوصاف احتياطية بسيطة"""
        name = mod_data.get('name', 'Minecraft Mod')
        category = mod_data.get('category', 'Mods')
        original_desc = mod_data.get('description', '')
        
        # وصف إنجليزي احتياطي
        english_desc = f"""
{name} is an exciting Minecraft mod that enhances your gaming experience. 
This {category.lower()} mod brings new features and improvements to your Minecraft world.

{original_desc[:200] if original_desc else 'Discover new possibilities and adventures with this amazing mod.'}

Perfect for players looking to expand their Minecraft experience with fresh content and innovative features.
""".strip()
        
        # وصف عربي احتياطي
        arabic_desc = f"""
{name} هو مود رائع لماين كرافت يعزز تجربة اللعب الخاصة بك.
هذا المود من فئة {category} يجلب ميزات جديدة وتحسينات إلى عالم ماين كرافت الخاص بك.

{original_desc[:200] if original_desc else 'اكتشف إمكانيات جديدة ومغامرات مثيرة مع هذا المود المذهل.'}

مثالي للاعبين الذين يتطلعون إلى توسيع تجربة ماين كرافت مع محتوى جديد وميزات مبتكرة.
""".strip()
        
        return english_desc, arabic_desc
    
    def enhance_mod_data(self, mod_data: Dict) -> Dict:
        """تحسين بيانات المود بإضافة الأوصاف المولدة"""
        try:
            # محاولة توليد أوصاف باستخدام النموذج اللغوي
            english_desc, arabic_desc = self.generate_descriptions(mod_data)
            
            # إذا فشل التوليد، استخدم الأوصاف الاحتياطية
            if not english_desc or not arabic_desc:
                self.logger.info("استخدام الأوصاف الاحتياطية")
                english_desc, arabic_desc = self.generate_fallback_descriptions(mod_data)
            
            # تحديث بيانات المود
            enhanced_data = mod_data.copy()
            enhanced_data['description'] = english_desc
            enhanced_data['description_ar'] = arabic_desc
            
            return enhanced_data
            
        except Exception as e:
            self.logger.error(f"خطأ في تحسين بيانات المود: {e}")
            return mod_data
