#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص سريع لمشكلة MCPEDL
"""

import requests
import os
from bs4 import BeautifulSoup

def test_mcpedl_access():
    """اختبار الوصول إلى MCPEDL"""
    print("🌐 اختبار الوصول إلى MCPEDL")
    print("=" * 40)
    
    url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
    
    # اختبار 1: طلب بسيط
    print("1️⃣ طلب بسيط...")
    try:
        response = requests.get(url, timeout=10)
        print(f"   رمز الاستجابة: {response.status_code}")
        print(f"   حجم المحتوى: {len(response.text)} حرف")
        
        # حفظ للفحص
        with open('debug_simple.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        
        # فحص سريع
        if '<title>' in response.text:
            print("   ✅ يحتوي على title")
        else:
            print("   ❌ لا يحتوي على title")
            
        if 'cloudflare' in response.text.lower():
            print("   🛡️ يحتوي على Cloudflare")
        
        if len(response.text) < 1000:
            print("   ⚠️ محتوى قصير جداً")
            print(f"   المحتوى: {response.text[:200]}...")
            
    except Exception as e:
        print(f"   ❌ خطأ: {e}")
    
    # اختبار 2: مع cloudscraper
    print("\n2️⃣ مع cloudscraper...")
    try:
        import cloudscraper
        
        scraper = cloudscraper.create_scraper()
        response = scraper.get(url, timeout=30)
        print(f"   رمز الاستجابة: {response.status_code}")
        print(f"   حجم المحتوى: {len(response.text)} حرف")
        
        # حفظ للفحص
        with open('debug_cloudscraper.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        
        # تحليل HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        title = soup.find('title')
        if title:
            print(f"   ✅ العنوان: {title.get_text()[:50]}...")
        else:
            print("   ❌ لا يوجد عنصر title")
        
        h1_tags = soup.find_all('h1')
        if h1_tags:
            print(f"   📌 عناوين H1: {len(h1_tags)}")
            for i, h1 in enumerate(h1_tags[:2]):
                print(f"      H1[{i}]: {h1.get_text()[:50]}...")
        else:
            print("   ❌ لا توجد عناوين H1")
        
        # البحث عن محتوى
        content_areas = soup.find_all(['article', 'main', 'div'], class_=['entry-content', 'post-content', 'content'])
        if content_areas:
            print(f"   📄 مناطق محتوى: {len(content_areas)}")
        else:
            print("   ❌ لا توجد مناطق محتوى")
            
    except ImportError:
        print("   ❌ cloudscraper غير مثبت")
    except Exception as e:
        print(f"   ❌ خطأ: {e}")

def analyze_saved_html():
    """تحليل HTML المحفوظ"""
    print("\n📄 تحليل HTML المحفوظ")
    print("=" * 40)
    
    # البحث عن ملف debug من الأداة الرئيسية
    debug_file = "send addons/debug_mcpedl_page.html"
    
    if os.path.exists(debug_file):
        print(f"📁 تحليل: {debug_file}")
        
        try:
            with open(debug_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"📏 حجم الملف: {len(content)} حرف")
            
            # عرض بداية المحتوى
            print("📝 بداية المحتوى:")
            print(content[:500])
            print("..." if len(content) > 500 else "")
            
            # البحث عن علامات مهمة
            if '<html' in content:
                print("✅ يحتوي على HTML tag")
            else:
                print("❌ لا يحتوي على HTML tag")
            
            if '<head' in content:
                print("✅ يحتوي على HEAD section")
            else:
                print("❌ لا يحتوي على HEAD section")
            
            if '<body' in content:
                print("✅ يحتوي على BODY tag")
            else:
                print("❌ لا يحتوي على BODY tag")
            
            if '<title>' in content:
                print("✅ يحتوي على TITLE tag")
            else:
                print("❌ لا يحتوي على TITLE tag")
                
        except Exception as e:
            print(f"❌ خطأ في قراءة الملف: {e}")
    else:
        print(f"⚠️ ملف غير موجود: {debug_file}")

def check_selenium_availability():
    """فحص توفر Selenium"""
    print("\n🤖 فحص Selenium")
    print("=" * 40)
    
    try:
        import selenium
        print("✅ Selenium مثبت")
        
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        print("✅ WebDriver متوفر")
        
        # اختبار Chrome
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            
            driver = webdriver.Chrome(options=chrome_options)
            driver.get("https://www.google.com")
            print("✅ Chrome WebDriver يعمل")
            driver.quit()
            
        except Exception as e:
            print(f"❌ مشكلة في Chrome WebDriver: {e}")
            print("💡 جرب: pip install webdriver-manager")
            
    except ImportError:
        print("❌ Selenium غير مثبت")
        print("💡 لتثبيته: pip install selenium webdriver-manager")

def main():
    """الدالة الرئيسية"""
    print("🔍 تشخيص سريع لمشكلة MCPEDL")
    print("=" * 50)
    
    # اختبار الوصول
    test_mcpedl_access()
    
    # تحليل HTML المحفوظ
    analyze_saved_html()
    
    # فحص Selenium
    check_selenium_availability()
    
    print("\n" + "=" * 50)
    print("📋 ملخص التشخيص:")
    print("1. إذا كان المحتوى قصير أو بدون title، فالموقع يحجب الطلبات")
    print("2. cloudscraper قد يساعد لكن ليس دائماً")
    print("3. Selenium هو الحل الأفضل للمواقع المعقدة")
    print("4. راجع ملفات debug_*.html للتفاصيل")
    
    print("\n💡 الحلول:")
    print("- python quick_selenium_install.py")
    print("- استخدام VPN")
    print("- المحاولة في وقت آخر")

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم الإيقاف")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        input("\nاضغط Enter للخروج...")
