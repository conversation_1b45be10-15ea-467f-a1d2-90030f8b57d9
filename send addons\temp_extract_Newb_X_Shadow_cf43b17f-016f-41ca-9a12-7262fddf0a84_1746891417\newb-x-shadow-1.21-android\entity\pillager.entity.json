{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "minecraft:pillager", "materials": {"default": "spider"}, "textures": {"default": "textures/entity/pillager"}, "geometry": {"default": "geometry.pillager"}, "spawn_egg": {"texture": "spawn_egg", "texture_index": 56}, "scripts": {"pre_animation": ["variable.tcos0 = (Math.cos(query.modified_distance_moved * 38.17) * query.modified_move_speed / variable.gliding_speed_value) * 57.3;"], "animate": ["pillager_root_controller"]}, "animations": {"humanoid_base_pose": "animation.humanoid.base_pose", "look_at_target_default": "animation.humanoid.look_at_target.default", "look_at_target_gliding": "animation.humanoid.look_at_target.gliding", "look_at_target_swimming": "animation.humanoid.look_at_target.swimming", "move": "animation.humanoid.move", "riding.arms": "animation.humanoid.riding.arms", "riding.legs": "animation.humanoid.riding.legs", "holding": "animation.humanoid.holding", "attack.rotations": "animation.humanoid.attack.rotations", "bob": "animation.humanoid.bob", "crossbow_hold": "animation.pillager.crossbow.hold", "crossbow_charge": "animation.pillager.crossbow.charge", "celebrating": "animation.humanoid.celebrating", "controller_humanoid_base_pose": "controller.animation.humanoid.base_pose", "controller_look_at_target": "controller.animation.humanoid.look_at_target", "controller_move": "controller.animation.humanoid.move", "controller_riding": "controller.animation.humanoid.riding", "controller_attack": "controller.animation.humanoid.attack", "controller_bob": "controller.animation.humanoid.bob", "controller_pillager_attack": "controller.animation.pillager.attack", "pillager_root_controller": "controller.animation.pillager.root"}, "render_controllers": ["controller.render.pillager"], "enable_attachables": true, "hide_armor": true}}}