# -*- coding: utf-8 -*-
"""
اختبار بسيط لاستخراج الصور المتعددة
"""

import sys
import os

# إضافة المجلد الحالي إلى المسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_image_extraction_directly():
    """اختبار استخراج الصور مباشرة"""
    print("🖼️ اختبار استخراج الصور مباشرة...")
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        import cloudscraper
        from bs4 import BeautifulSoup
        
        # إنشاء مستخرج
        extractor = MCPEDLExtractorFixed()
        
        # جلب HTML مباشرة
        scraper = cloudscraper.create_scraper()
        
        test_url = "https://mcpedl.com/ezrtx/"
        print(f"🔗 جلب الصفحة: {test_url}")
        
        response = scraper.get(test_url, timeout=30)
        
        if response.status_code == 200:
            print("✅ تم جلب HTML بنجاح")
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # استخراج الصور مباشرة
            print("🔍 استخراج الصور...")
            
            # البحث عن جميع الصور
            all_images = soup.find_all('img')
            print(f"📊 تم العثور على {len(all_images)} عنصر img")
            
            # استخراج الروابط
            image_urls = []
            
            for img in all_images:
                src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
                if src:
                    # تحويل الروابط النسبية إلى مطلقة
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = 'https://mcpedl.com' + src
                    
                    # فحص صحة الصورة
                    if extractor.is_valid_mod_image(src) and src not in image_urls:
                        image_urls.append(src)
            
            print(f"✅ تم استخراج {len(image_urls)} صورة صحيحة")
            
            # عرض الصور
            for i, img_url in enumerate(image_urls, 1):
                print(f"   {i}. {img_url}")
            
            # تحليل أنواع الصور
            forgecdn_count = sum(1 for img in image_urls if 'media.forgecdn.net' in img)
            mcpedl_count = sum(1 for img in image_urls if 'mcpedl.com' in img and 'users' not in img)
            user_count = sum(1 for img in image_urls if 'users' in img)
            
            print(f"\n📊 تحليل الصور:")
            print(f"   - صور forgecdn: {forgecdn_count}")
            print(f"   - صور mcpedl: {mcpedl_count}")
            print(f"   - صور مستخدمين: {user_count}")
            
            if len(image_urls) >= 3:
                print("🎉 ممتاز! تم استخراج صور متعددة")
                return True
            elif len(image_urls) >= 1:
                print("⚠️ تم استخراج بعض الصور")
                return True
            else:
                print("❌ لم يتم استخراج أي صور")
                return False
        else:
            print(f"❌ فشل في جلب الصفحة: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        print(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def test_specific_image_urls():
    """اختبار روابط صور محددة"""
    print("\n🎯 اختبار روابط صور محددة...")
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        
        extractor = MCPEDLExtractorFixed()
        
        # روابط صور متوقعة من ezRTX
        test_images = [
            "https://media.forgecdn.net/attachments/1180/464/ezrtx-png.png",
            "https://media.forgecdn.net/attachments/1113/871/ezrtx-5-jpg.jpg",
            "https://media.forgecdn.net/attachments/1113/870/ezrtx-4-jpg.jpg",
            "https://media.forgecdn.net/attachments/1113/869/ezrtx-3-jpg.jpg",
            "https://media.forgecdn.net/attachments/1113/868/ezrtx-2-jpg.jpg",
            "https://media.forgecdn.net/attachments/1113/867/ezrtx-1-jpg.jpg",
        ]
        
        valid_images = []
        
        for img_url in test_images:
            if extractor.is_valid_mod_image(img_url):
                valid_images.append(img_url)
        
        print(f"✅ تم قبول {len(valid_images)} من {len(test_images)} صورة")
        
        for img_url in valid_images:
            print(f"   ✅ {img_url}")
        
        return len(valid_images) >= 3
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def test_image_filtering():
    """اختبار فلترة الصور"""
    print("\n🔍 اختبار فلترة الصور...")
    
    try:
        from mcpedl_extractor_fixed import MCPEDLExtractorFixed
        
        extractor = MCPEDLExtractorFixed()
        
        # صور يجب قبولها
        good_images = [
            "https://media.forgecdn.net/attachments/1113/871/ezrtx-5-jpg.jpg",
            "https://mcpedl.com/wp-content/uploads/2023/mod-image.png",
            "https://r2.mcpedl.com/content/mod-screenshot.png",
        ]
        
        # صور يجب رفضها
        bad_images = [
            "https://secure.gravatar.com/avatar/4d709253272132cf114cba539b5fa0b0",
            "https://r2.mcpedl.com/users/3445213/avatar.png",
            "https://example.com/profile_pic.jpg",
        ]
        
        correct_accepts = 0
        correct_rejects = 0
        
        print("📋 اختبار الصور الجيدة:")
        for img_url in good_images:
            result = extractor.is_valid_mod_image(img_url)
            if result:
                correct_accepts += 1
                print(f"   ✅ {img_url[:60]}...")
            else:
                print(f"   ❌ {img_url[:60]}...")
        
        print("📋 اختبار الصور السيئة:")
        for img_url in bad_images:
            result = extractor.is_valid_mod_image(img_url)
            if not result:
                correct_rejects += 1
                print(f"   ✅ {img_url[:60]}...")
            else:
                print(f"   ❌ {img_url[:60]}...")
        
        total_correct = correct_accepts + correct_rejects
        total_tests = len(good_images) + len(bad_images)
        
        accuracy = (total_correct / total_tests) * 100
        print(f"\n📊 دقة الفلترة: {accuracy:.1f}% ({total_correct}/{total_tests})")
        
        return accuracy >= 80
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار بسيط لاستخراج الصور المتعددة")
    print("=" * 60)
    
    results = {}
    
    # اختبار استخراج مباشر
    results['direct_extraction'] = test_image_extraction_directly()
    
    # اختبار روابط محددة
    results['specific_urls'] = test_specific_image_urls()
    
    # اختبار الفلترة
    results['filtering'] = test_image_filtering()
    
    # تلخيص النتائج
    print("\n📊 ملخص النتائج:")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name}: {status}")
    
    success_rate = sum(results.values()) / len(results)
    print(f"\nمعدل النجاح: {success_rate*100:.1f}%")
    
    if success_rate >= 0.8:
        print("\n🎉 استخراج الصور يعمل بشكل ممتاز!")
        print("✅ الفلترة تعمل بشكل صحيح")
        print("✅ يمكن استخراج صور متعددة")
    elif success_rate >= 0.5:
        print("\n👍 استخراج الصور يعمل جزئياً")
        print("⚠️ قد يحتاج بعض التحسينات")
    else:
        print("\n⚠️ استخراج الصور يحتاج مراجعة")

if __name__ == "__main__":
    main()
