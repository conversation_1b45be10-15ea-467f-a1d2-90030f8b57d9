"""
دوال مساعدة للتطبيق
"""
import re
import time
import logging
from urllib.parse import urljoin, urlparse
from typing import List, Optional, Dict, Any

def setup_logging(level=logging.INFO):
    """إعداد نظام التسجيل"""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('mcpedl_scraper.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def is_valid_mcpedl_url(url: str) -> bool:
    """التحقق من صحة رابط mcpedl.com"""
    try:
        parsed = urlparse(url)
        return parsed.netloc.lower() in ['mcpedl.com', 'www.mcpedl.com']
    except:
        return False

def clean_text(text: str) -> str:
    """تنظيف النص من المسافات الزائدة والأحرف الخاصة"""
    if not text:
        return ""
    
    # إزالة المسافات الزائدة والأسطر الجديدة
    text = re.sub(r'\s+', ' ', text.strip())
    
    # إزالة الأحرف الخاصة غير المرغوب فيها
    text = re.sub(r'[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF.,!?()-]', '', text)
    
    return text.strip()

def extract_file_size(text: str) -> Optional[str]:
    """استخراج حجم الملف من النص"""
    if not text:
        return None
    
    # البحث عن أنماط حجم الملف (MB, KB, GB)
    size_pattern = r'(\d+(?:\.\d+)?)\s*(KB|MB|GB|kb|mb|gb)'
    match = re.search(size_pattern, text, re.IGNORECASE)
    
    if match:
        size, unit = match.groups()
        return f"{size} {unit.upper()}"
    
    return None

def extract_version_numbers(text: str) -> List[str]:
    """استخراج أرقام إصدارات Minecraft من النص"""
    if not text:
        return []
    
    # البحث عن أنماط الإصدارات (1.19, 1.20.1, etc.)
    version_pattern = r'1\.\d+(?:\.\d+)*'
    versions = re.findall(version_pattern, text)
    
    # إزالة التكرارات والترتيب
    return sorted(list(set(versions)), reverse=True)

def make_absolute_url(base_url: str, relative_url: str) -> str:
    """تحويل الرابط النسبي إلى مطلق"""
    if not relative_url:
        return ""
    
    if relative_url.startswith(('http://', 'https://')):
        return relative_url
    
    return urljoin(base_url, relative_url)

def extract_social_platform(url: str) -> str:
    """تحديد منصة التواصل الاجتماعي من الرابط"""
    if not url:
        return "unknown"
    
    url_lower = url.lower()
    
    if 'youtube.com' in url_lower or 'youtu.be' in url_lower:
        return 'YouTube'
    elif 'discord' in url_lower:
        return 'Discord'
    elif 'twitter.com' in url_lower or 'x.com' in url_lower:
        return 'Twitter/X'
    elif 'instagram.com' in url_lower:
        return 'Instagram'
    elif 'facebook.com' in url_lower:
        return 'Facebook'
    elif 'tiktok.com' in url_lower:
        return 'TikTok'
    elif 'github.com' in url_lower:
        return 'GitHub'
    else:
        return 'Other'

def safe_int_convert(value: Any, default: int = 0) -> int:
    """تحويل آمن للقيمة إلى رقم صحيح"""
    if value is None:
        return default
    
    if isinstance(value, int):
        return value
    
    if isinstance(value, str):
        # إزالة الفاصلات والمسافات
        value = re.sub(r'[,\s]', '', value)
        # استخراج الأرقام فقط
        numbers = re.findall(r'\d+', value)
        if numbers:
            try:
                return int(numbers[0])
            except ValueError:
                pass
    
    return default

def delay_request(seconds: float = 2.0):
    """تأخير بين الطلبات لتجنب الحظر"""
    time.sleep(seconds)

def validate_image_url(url: str) -> bool:
    """التحقق من صحة رابط الصورة"""
    if not url:
        return False
    
    # التحقق من امتداد الصورة
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
    url_lower = url.lower()
    
    return any(ext in url_lower for ext in image_extensions)

def truncate_text(text: str, max_length: int = 500) -> str:
    """قطع النص إلى طول محدد مع الحفاظ على الكلمات"""
    if not text or len(text) <= max_length:
        return text
    
    truncated = text[:max_length]
    last_space = truncated.rfind(' ')
    
    if last_space > 0:
        truncated = truncated[:last_space]
    
    return truncated + "..."
