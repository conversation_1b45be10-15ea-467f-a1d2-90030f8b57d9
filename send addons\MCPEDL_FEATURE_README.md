# ميزة استخراج المودات من MCPEDL - دليل الاستخدام

## نظرة عامة

تم إضافة ميزة جديدة قوية إلى أداة نشر المودات تتيح لك استخراج بيانات المودات تلقائياً من موقع mcpedl.com وملء جميع الحقول في الأداة بنقرة واحدة.

## الميزات الجديدة

### 🔍 استخراج تلقائي شامل
- **اسم المود**: يتم استخراجه من عنوان الصفحة
- **الوصف**: استخراج الوصف الكامل من محتوى الصفحة
- **الفئة**: تحديد فئة المود تلقائياً وتحويلها للفئات المدعومة
- **الإصدارات المدعومة**: استخراج إصدارات Minecraft المتوافقة
- **الصور**: جمع جميع الصور (الرئيسية والمعرض)
- **معلومات المطور**: اسم المطور ومعلومات التواصل
- **قنوات التواصل الاجتماعي**: روابط YouTube, Discord, Twitter, إلخ
- **رابط التحميل**: البحث عن رابط تحميل المود
- **حجم الملف**: استخراج حجم ملف المود

### 🎯 ملء تلقائي للحقول
بعد الاستخراج، يتم ملء الحقول التالية تلقائياً:
- حقل اسم المود
- حقل الوصف (الإنجليزي)
- قائمة الفئات
- حقل الإصدار
- حقل الحجم
- حقل رابط التحميل
- حقل اسم المطور
- حقول قنوات التواصل الاجتماعي
- قائمة الصور المدارة
- الصورة الرئيسية

## كيفية الاستخدام

### الخطوة 1: تحضير الرابط
1. انتقل إلى موقع mcpedl.com
2. ابحث عن المود المطلوب
3. انسخ رابط صفحة المود
   - مثال: `https://mcpedl.com/dragon-mounts-v1-3-25/`

### الخطوة 2: استخدام الميزة في الأداة
1. شغل أداة نشر المودات
2. ابحث عن قسم **"4.5. استخراج من MCPEDL"**
3. الصق رابط صفحة المود في الحقل المخصص
4. اضغط على زر **"استخراج البيانات من MCPEDL"**
5. انتظر حتى تكتمل عملية الاستخراج
6. ستجد جميع الحقول مملوءة تلقائياً!

### الخطوة 3: المراجعة والتعديل
- راجع البيانات المستخرجة
- عدل أي معلومات حسب الحاجة
- أضف أي تفاصيل إضافية
- تابع عملية النشر كالمعتاد

## الملفات المضافة

### `mcpedl_scraper_module.py`
وحدة استخراج البيانات الرئيسية التي تحتوي على:
- كلاس `MCPEDLScraper` للاستخراج
- دوال تحليل HTML
- دوال استخراج البيانات المختلفة
- معالجة الأخطاء والاستثناءات

### التحديثات على `mod_processor.py`
- إضافة دوال معالجة MCPEDL
- إضافة واجهة المستخدم الجديدة
- دمج الميزة مع النظام الموجود
- دوال ملء الحقول التلقائي

## المتطلبات التقنية

### المكتبات المطلوبة
```python
requests>=2.31.0
beautifulsoup4>=4.12.2
lxml>=4.9.3
```

### التحقق من التوفر
الأداة تتحقق تلقائياً من توفر وحدة MCPEDL:
- إذا كانت متوفرة: يظهر قسم الاستخراج
- إذا لم تكن متوفرة: يتم إخفاء القسم مع رسالة تحذيرية

## الميزات المتقدمة

### 🔄 تحويل الفئات الذكي
يتم تحويل فئات MCPEDL إلى الفئات المدعومة في الأداة:
- `Addons/Mods/Behavior` → `Addons`
- `Shaders` → `Shaders`
- `Texture/Resource Pack` → `Texture Pack`

### 🖼️ إدارة الصور المتقدمة
- استخراج الصورة الرئيسية تلقائياً
- جمع صور المعرض (حد أقصى 10 صور)
- إضافة الصور إلى نظام إدارة الصور
- تعيين الصورة الأولى كصورة رئيسية

### 🌐 معالجة قنوات التواصل
- تحديد نوع المنصة تلقائياً
- إضافة حقول جديدة حسب الحاجة
- تنسيق الروابط بشكل صحيح

## معالجة الأخطاء

### الأخطاء الشائعة وحلولها

#### "وحدة استخراج MCPEDL غير متوفرة"
**الحل**: تأكد من وجود ملف `mcpedl_scraper_module.py` في نفس مجلد الأداة

#### "رابط غير صحيح"
**الحل**: تأكد من أن الرابط من موقع mcpedl.com
- ✅ صحيح: `https://mcpedl.com/mod-name/`
- ❌ خاطئ: `https://google.com`

#### "فشل في استخراج البيانات"
**الأسباب المحتملة**:
- مشكلة في الاتصال بالإنترنت
- تغيير في هيكل موقع MCPEDL
- الصفحة غير موجودة أو محذوفة

**الحلول**:
1. تحقق من الاتصال بالإنترنت
2. تأكد من صحة الرابط
3. جرب رابط مود آخر

### نصائح للاستخدام الأمثل

#### 🎯 اختيار المودات المناسبة
- اختر مودات حديثة ومحدثة
- تجنب المودات القديمة جداً
- تأكد من أن الصفحة تحتوي على معلومات كاملة

#### ⚡ تحسين الأداء
- لا تستخرج عدة مودات في نفس الوقت
- انتظر انتهاء الاستخراج قبل البدء بآخر
- راجع البيانات قبل النشر

#### 🔧 التخصيص
- يمكن تعديل البيانات المستخرجة
- أضف معلومات إضافية حسب الحاجة
- استخدم ميزة AI لتحسين الأوصاف

## الدعم والمساعدة

### في حالة وجود مشاكل:
1. تحقق من سجل الحالة في الأداة
2. تأكد من توفر جميع المتطلبات
3. جرب إعادة تشغيل الأداة
4. تحقق من تحديثات الأداة

### تحسينات مستقبلية مخططة:
- دعم مواقع مودات أخرى
- تحسين دقة الاستخراج
- إضافة خيارات تخصيص أكثر
- دعم الاستخراج المتعدد

## الخلاصة

ميزة استخراج MCPEDL تجعل عملية نشر المودات أسرع وأسهل بكثير. بدلاً من ملء كل حقل يدوياً، يمكنك الآن استخراج جميع البيانات بنقرة واحدة والتركيز على المراجعة والتحسين.

هذه الميزة توفر الوقت والجهد وتقلل من الأخطاء، مما يجعل عملية نشر المودات أكثر كفاءة ومتعة! 🚀
