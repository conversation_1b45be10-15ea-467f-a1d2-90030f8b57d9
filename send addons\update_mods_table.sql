-- SQL script to update existing mods table for the new custom social sites system
-- This script removes old columns and ensures compatibility with the new system

-- Note: The new system stores all custom social sites in the existing creator_social_channels JSONB column
-- No new columns are needed as we now use a dynamic multi-site interface

-- Check current table structure
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'mods'
ORDER BY ordinal_position;

-- Verify the changes
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'mods'
AND column_name IN ('custom_social_site_name', 'custom_social_site_url')
ORDER BY column_name;

-- Sample query to test the new columns
/*
-- Update an existing mod with custom social site information
UPDATE mods
SET
    custom_social_site_name = 'Creator Forum',
    custom_social_site_url = 'https://creator-forum.example.com'
WHERE id = 'your-mod-id-here';

-- Select mods with custom social sites
SELECT
    name,
    creator_name,
    custom_social_site_name,
    custom_social_site_url
FROM mods
WHERE custom_social_site_name IS NOT NULL
   OR custom_social_site_url IS NOT NULL;
*/
