"""
ملف التشغيل السريع لاختبار أداة استخراج مودات mcpedl.com
"""
import os
import sys
from dotenv import load_dotenv

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    print("🔍 التحقق من المتطلبات...")
    
    # التحقق من Python version
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # التحقق من المكتبات المطلوبة
    required_packages = [
        'requests', 'beautifulsoup4', 'supabase', 
        'python-dotenv', 'openai', 'lxml'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} غير مثبت")
    
    if missing_packages:
        print(f"\n📦 لتثبيت المكتبات المفقودة:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_environment():
    """التحقق من متغيرات البيئة"""
    print("\n🔧 التحقق من متغيرات البيئة...")
    
    # تحميل متغيرات البيئة
    load_dotenv()
    
    # التحقق من ملف .env
    if not os.path.exists('.env'):
        print("❌ ملف .env غير موجود")
        print("📝 قم بنسخ .env.example إلى .env وأدخل القيم المطلوبة")
        return False
    
    print("✅ ملف .env موجود")
    
    # التحقق من المتغيرات المطلوبة
    required_vars = {
        'SUPABASE_URL': 'رابط مشروع Supabase',
        'SUPABASE_SERVICE_KEY': 'مفتاح خدمة Supabase'
    }
    
    optional_vars = {
        'OPENAI_API_KEY': 'مفتاح OpenAI (اختياري لتوليد الأوصاف)'
    }
    
    all_good = True
    
    for var, description in required_vars.items():
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * 10}...{value[-4:] if len(value) > 4 else '****'}")
        else:
            print(f"❌ {var}: غير محدد ({description})")
            all_good = False
    
    for var, description in optional_vars.items():
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * 10}...{value[-4:] if len(value) > 4 else '****'}")
        else:
            print(f"⚠️ {var}: غير محدد ({description})")
    
    return all_good

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    print("\n🧪 اختبار الوظائف الأساسية...")
    
    try:
        # اختبار استيراد الوحدات
        from config import Config
        from utils import is_valid_mcpedl_url
        from scraper import MCPEDLScraper
        
        print("✅ تم استيراد الوحدات بنجاح")
        
        # اختبار التحقق من الروابط
        test_url = "https://mcpedl.com/test-mod/"
        if is_valid_mcpedl_url(test_url):
            print("✅ دالة التحقق من الروابط تعمل")
        else:
            print("❌ مشكلة في دالة التحقق من الروابط")
            return False
        
        # اختبار إنشاء المستخرج
        scraper = MCPEDLScraper()
        scraper.close()
        print("✅ تم إنشاء المستخرج بنجاح")
        
        # اختبار الاتصال بـ Supabase
        try:
            from supabase_client import SupabaseClient
            supabase_client = SupabaseClient()
            print("✅ تم الاتصال بـ Supabase بنجاح")
        except Exception as e:
            print(f"❌ خطأ في الاتصال بـ Supabase: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الوظائف: {e}")
        return False

def run_sample_extraction():
    """تشغيل استخراج عينة"""
    print("\n🚀 تشغيل استخراج عينة...")
    
    # رابط مود للاختبار (يمكن تغييره)
    test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
    
    try:
        from main import MCPEDLModExtractor
        
        print(f"📥 محاولة استخراج: {test_url}")
        
        extractor = MCPEDLModExtractor()
        success = extractor.process_single_mod(test_url, skip_existing=False)
        extractor.close()
        
        if success:
            print("✅ تم الاستخراج بنجاح!")
            print("🎉 الأداة جاهزة للاستخدام!")
            return True
        else:
            print("❌ فشل في الاستخراج")
            print("💡 تحقق من الرابط والاتصال بالإنترنت")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاستخراج: {e}")
        return False

def show_usage_examples():
    """عرض أمثلة الاستخدام"""
    print("\n📚 أمثلة الاستخدام:")
    print("=" * 50)
    
    examples = [
        ("استخراج مود واحد", "python main.py --url 'https://mcpedl.com/mod-name/'"),
        ("استخراج من ملف", "python main.py --file urls.txt"),
        ("تحليل البيانات", "python cli_manager.py analyze --report"),
        ("البحث في قاعدة البيانات", "python cli_manager.py db --search 'dragon'"),
        ("تصدير البيانات", "python cli_manager.py db --export-all mods_backup.json"),
        ("اختبار الأداة", "python test_scraper.py"),
    ]
    
    for description, command in examples:
        print(f"\n{description}:")
        print(f"  {command}")
    
    print("\n" + "=" * 50)
    print("📖 للمزيد من التفاصيل، راجع ملف README.md")

def main():
    """الدالة الرئيسية للتشغيل السريع"""
    print("🎯 أداة استخراج بيانات مودات Minecraft من mcpedl.com")
    print("=" * 60)
    
    # التحقق من المتطلبات
    if not check_requirements():
        print("\n❌ يرجى تثبيت المتطلبات المفقودة أولاً")
        return False
    
    # التحقق من البيئة
    if not check_environment():
        print("\n❌ يرجى إعداد متغيرات البيئة أولاً")
        return False
    
    # اختبار الوظائف الأساسية
    if not test_basic_functionality():
        print("\n❌ مشكلة في الوظائف الأساسية")
        return False
    
    print("\n✅ جميع الفحوصات نجحت!")
    
    # سؤال المستخدم عن تشغيل استخراج عينة
    try:
        response = input("\n❓ هل تريد تشغيل استخراج عينة؟ (y/n): ").lower().strip()
        
        if response in ['y', 'yes', 'نعم', 'ن']:
            if run_sample_extraction():
                print("\n🎉 تم إعداد الأداة بنجاح!")
            else:
                print("\n⚠️ الأداة جاهزة لكن الاستخراج التجريبي فشل")
        else:
            print("\n✅ الأداة جاهزة للاستخدام!")
    
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التشغيل")
    
    # عرض أمثلة الاستخدام
    show_usage_examples()
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
