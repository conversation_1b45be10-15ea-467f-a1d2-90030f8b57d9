#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحسينات الجديدة لاستخراج MCPEDL
"""

import sys
import os

# إضافة مجلد send addons إلى المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
addons_dir = os.path.join(current_dir, 'send addons')
sys.path.insert(0, addons_dir)

# طباعة المسارات للتشخيص
print(f"المسار الحالي: {current_dir}")
print(f"مسار send addons: {addons_dir}")
print(f"قائمة المسارات: {sys.path}")

# التحقق من وجود الملفات
mcpedl_file = os.path.join(addons_dir, 'mcpedl_selenium_scraper.py')
if os.path.exists(mcpedl_file):
    print(f"✅ ملف mcpedl_selenium_scraper.py موجود")
else:
    print(f"❌ ملف mcpedl_selenium_scraper.py غير موجود")

def test_enhanced_extraction():
    """اختبار الاستخراج المحسن"""
    print("🧪 اختبار الاستخراج المحسن من MCPEDL")
    print("=" * 50)

    try:
        from mcpedl_selenium_scraper import scrape_mcpedl_with_selenium, SELENIUM_AVAILABLE

        if not SELENIUM_AVAILABLE:
            print("❌ Selenium غير متوفر")
            print("💡 لتثبيته: pip install selenium webdriver-manager")
            return False

        # رابط للاختبار
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"

        print(f"📥 اختبار استخراج: {test_url}")
        print("⏳ جاري الاستخراج...")

        # استخراج البيانات
        mod_data = scrape_mcpedl_with_selenium(test_url)

        if not mod_data:
            print("❌ فشل في الاستخراج")
            return False

        print("✅ تم الاستخراج بنجاح!")
        print("\n📋 تحليل البيانات المستخرجة:")

        # تحليل الاسم
        name = mod_data.get('name', '')
        if name:
            print(f"✅ الاسم: {name}")
        else:
            print("❌ الاسم مفقود")

        # تحليل الوصف
        description = mod_data.get('description', '')
        if description:
            print(f"✅ الوصف: {len(description)} حرف")
            print(f"📝 بداية الوصف: {description[:100]}...")

            # التحقق من جودة الوصف
            if 'enhance your gameplay' in description and len(description) < 200:
                print("⚠️ الوصف عام جداً")
            elif 'هو' in description or 'مبتكر' in description:
                print("✅ الوصف يحتوي على نص عربي مخصص")
            else:
                print("✅ الوصف مفصل")
        else:
            print("❌ الوصف مفقود")

        # تحليل الصور
        images = mod_data.get('image_urls', [])
        if images:
            print(f"✅ الصور: {len(images)} صورة")

            # فحص جودة الصور
            valid_images = 0
            for i, img in enumerate(images[:5]):
                print(f"  📷 [{i+1}]: {img[:60]}...")

                # فحص الصور الثابتة
                if any(bad_img in img.lower() for bad_img in ['shield.png', '/users/', 'data:image/svg']):
                    print(f"    ⚠️ صورة ثابتة أو غير مرغوبة")
                else:
                    valid_images += 1
                    print(f"    ✅ صورة صالحة")

            print(f"  📊 الصور الصالحة: {valid_images}/{len(images)}")
        else:
            print("❌ لا توجد صور")

        # تحليل الفئة
        category = mod_data.get('category', '')
        if category:
            print(f"✅ الفئة: {category}")
        else:
            print("❌ الفئة مفقودة")

        # تحليل الإصدار
        version = mod_data.get('version', '')
        if version:
            print(f"✅ الإصدار: {version}")
        else:
            print("❌ الإصدار مفقود")

        # تحليل المطور
        creator = mod_data.get('creator_name', '')
        if creator:
            print(f"✅ المطور: {creator}")

            # فحص جودة اسم المطور
            if any(unwanted in creator for unwanted in ['Published on', 'Updated on', 'CurseForge', '2024', '2025']):
                print(f"    ⚠️ اسم المطور يحتوي على نصوص إضافية")
            else:
                print(f"    ✅ اسم المطور نظيف")
        else:
            print("⚠️ اسم المطور مفقود")

        # تحليل روابط التحميل
        download_url = mod_data.get('download_url', '')
        if download_url:
            print(f"✅ رابط التحميل: {download_url[:50]}...")
        else:
            print("⚠️ رابط التحميل مفقود")

        # تحليل قنوات التواصل
        social_channels = mod_data.get('creator_social_channels', [])
        if social_channels:
            print(f"✅ قنوات التواصل: {len(social_channels)}")

            # فحص جودة قنوات التواصل
            valid_channels = 0
            for channel in social_channels[:3]:
                print(f"  🔗 {channel}")

                # فحص الروابط الثابتة
                if 'data:image/svg' in channel or 'mcpedl.com' in channel:
                    print(f"    ⚠️ رابط ثابت أو داخلي")
                else:
                    valid_channels += 1
                    print(f"    ✅ رابط صالح")

            print(f"  📊 القنوات الصالحة: {valid_channels}/{len(social_channels)}")
        else:
            print("⚠️ قنوات التواصل مفقودة")

        # حساب نقاط الجودة
        quality_score = 0
        total_fields = 8

        if name: quality_score += 1
        if description and len(description) > 100: quality_score += 1
        if images: quality_score += 1
        if category: quality_score += 1
        if version: quality_score += 1
        if creator: quality_score += 1
        if download_url: quality_score += 1
        if social_channels: quality_score += 1

        quality_percentage = (quality_score / total_fields) * 100

        print(f"\n📊 نقاط الجودة: {quality_score}/{total_fields} ({quality_percentage:.1f}%)")

        if quality_percentage >= 80:
            print("🎉 جودة ممتازة!")
        elif quality_percentage >= 60:
            print("👍 جودة جيدة")
        else:
            print("⚠️ جودة متوسطة")

        return True

    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def test_description_generation():
    """اختبار إنشاء الوصف المخصص"""
    print("\n📝 اختبار إنشاء الوصف المخصص")
    print("=" * 50)

    try:
        from mcpedl_selenium_scraper import EnhancedMCPEDLExtractor
        from bs4 import BeautifulSoup

        extractor = EnhancedMCPEDLExtractor()

        # بيانات تجريبية
        test_mod_data = {
            'name': 'Dragon Mounts Community Edition',
            'category': 'Addons'
        }

        # HTML تجريبي
        test_html = """
        <div class="entry-content">
            <p>This addon adds amazing dragons to your world.</p>
            <ul>
                <li>Rideable dragons</li>
                <li>Multiple dragon types</li>
                <li>Dragon breeding system</li>
            </ul>
        </div>
        """

        soup = BeautifulSoup(test_html, 'html.parser')

        # إنشاء وصف مخصص
        custom_description = extractor.create_custom_description(test_mod_data, soup)

        print("✅ تم إنشاء وصف مخصص:")
        print(custom_description)

        # التحقق من وجود النص العربي
        if 'هو' in custom_description and 'مبتكر' in custom_description:
            print("✅ يحتوي على نص عربي")
        else:
            print("❌ لا يحتوي على نص عربي")

        # التحقق من وجود النص الإنجليزي
        if 'innovative' in custom_description and 'enhances' in custom_description:
            print("✅ يحتوي على نص إنجليزي")
        else:
            print("❌ لا يحتوي على نص إنجليزي")

        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار الوصف: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار التحسينات الجديدة لاستخراج MCPEDL")
    print("=" * 60)

    success_count = 0
    total_tests = 2

    # اختبار إنشاء الوصف
    if test_description_generation():
        success_count += 1

    # اختبار الاستخراج الكامل
    print("\n" + "="*60)
    response = input("هل تريد اختبار الاستخراج الكامل؟ (قد يستغرق وقتاً) [y/n]: ").lower().strip()

    if response in ['y', 'yes', 'نعم', 'ن']:
        if test_enhanced_extraction():
            success_count += 1
    else:
        print("⏭️ تم تخطي اختبار الاستخراج الكامل")
        total_tests = 1

    # النتائج
    print("\n" + "=" * 60)
    print(f"📊 النتائج: {success_count}/{total_tests} اختبارات نجحت")

    if success_count == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ التحسينات تعمل بشكل صحيح")
    elif success_count > 0:
        print("⚠️ بعض الاختبارات نجحت")
    else:
        print("❌ فشلت جميع الاختبارات")

    print("\n💡 الآن يمكنك تجربة الأداة الرئيسية:")
    print('python "send addons/mod_processor.py"')

if __name__ == "__main__":
    try:
        main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        input("\nاضغط Enter للخروج...")
